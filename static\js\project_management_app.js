// 导入项目管理工具函数
import { initializeProjectManagement } from '/cdn/js/pm_utils.js';
import { initializeMemberManagement } from '/cdn/js/pm_members.js';
import { initializeTaskManagement } from '/cdn/js/pm_tasks.js';
import { initializeHomeFeatures } from '/cdn/js/pm_home.js';
import { initializeCharts } from '/cdn/js/pm_charts.js';
import { initializeTaskHandler } from '/cdn/js/pm_task_handler.js';
import { initializeTemplateManagement } from '/cdn/js/pm_templates.js';
import { initializeTaskDeleteApproval } from '/cdn/js/pm_task_delete_approval.js';

// 从页面中获取初始化数据
    try {
        const initDataElement = document.getElementById('init-data');
        
        // 需要调试问题显示原始内容
        // console.log("JSON原始内容:", initDataElement.textContent);
        
        const initData = JSON.parse(initDataElement.textContent);

        const { createApp, ref, computed, onMounted, watch } = Vue;
        
        // 预先获取用户ID并缓存
        fetch('/api/user_id')
            .then(response => response.json())
            .then(data => {
                if (data && data.user_id) {
                    localStorage.setItem('current_user_id', data.user_id);
                    console.log('应用初始化时已缓存用户ID:', data.user_id);
                }
            })
            .catch(error => {
                console.error('应用初始化时获取用户ID失败:', error);
            });
    
    // 添加获取当前用户ID的逻辑
    const getCurrentUserId = () => {
        // 首先尝试从initData中获取当前用户ID
        if (initData.userData && initData.userData.id) {
            return initData.userData.id;
        }
        
        // 从localStorage中获取缓存的用户ID
        const cachedUserId = localStorage.getItem('current_user_id');
        if (cachedUserId) {
            return cachedUserId;
        }
        
        // 如果没有缓存，则返回null，并发起异步请求获取用户ID
        // 异步获取用户ID并缓存
        fetch('/api/user_id')
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('获取用户ID失败');
            })
            .then(data => {
                if (data && data.user_id) {
                    // 缓存用户ID到localStorage
                    localStorage.setItem('current_user_id', data.user_id);
                    console.log('用户ID已缓存:', data.user_id);
                }
            })
            .catch(error => {
                console.error('获取用户ID请求失败:', error);
            });
        
        // 如果无法获取用户ID，返回null
        return null;
    };

    const app = createApp({
        setup() {
            // 从pm_utils.js导入基础功能和状态
            const pmUtils = initializeProjectManagement(initData, Vue, ElementPlus);
            
            // 导入基础状态和功能
            const userInfo = pmUtils.userInfo;
            const projects = pmUtils.projects;
            const currentProject = pmUtils.currentProject;
            const projectSearch = pmUtils.projectSearch;
            const filteredProjects = pmUtils.filteredProjects;
            const filteredManageableProjects = pmUtils.filteredManageableProjects;
            const currentView = pmUtils.currentView;
            const searchQuery = pmUtils.searchQuery;
            const tasks = pmUtils.tasks;
            const tableLoading = pmUtils.tableLoading;
            const navItems = pmUtils.navItems;
            const drawer = pmUtils.drawer;

            // 确保drawer对象包含replyMessage属性
            drawer.value.replyMessage = '';
            drawer.value.replyHtml = '';
            drawer.value.replyText = '';

            // 添加计算属性
            const canSendReply = computed(() => {
                return drawer.value.replyText && drawer.value.replyText.trim().length > 0;
            });

            const pageTitle = computed(() => {
                if (currentView.value === 'search') {
                    return searchQuery.value ? `搜索结果: ${searchQuery.value}` : "搜索任务";
                } else if (currentView.value === 'completed') {
                    return "已完成的任务";
                } else if (currentView.value === 'created') {
                    return "我发起的任务";
                } else if (currentView.value === 'received') {
                    return "我的关注";
                } else if (currentView.value === 'manage') {
                    return "项目管理";
                } else if (currentView.value === 'stats') {
                    // 根据是否有选中的项目（包括 all）显示不同标题
                    return (currentProject.value && currentProject.value.id) ? "项目统计" : "选择项目以查看统计"; 
                } else if (currentView.value === 'templates') {
                    return "模板管理";
                } else if (currentView.value === 'template_details') {
                    // 安全地访问模板名称
                    return currentTemplateDetails.value ? `模板详情: ${currentTemplateDetails.value.name}` : "加载模板详情...";
                } else if (currentView.value === 'approval') {
                    return "任务审批";
                } else if (currentView.value === 'home') {
                    return ""; // 首页通常没有主标题
                } else if (currentProject.value && currentProject.value.id) {
                    // 默认的项目视图标题
                    return `项目: ${currentProject.value.name}`;
                } else if (currentView.value === 'task_stats') {
                    // 默认的项目视图标题
                    return `任务统计`;
                } else {
                    return "无可见项目或模板";
                }
            });
            
            const projectDialog = pmUtils.projectDialog;
            const openProjectManagement = pmUtils.openProjectManagement;
            const loadProjectMembers = pmUtils.loadProjectMembers;
            const loadAvailableUsers = pmUtils.loadAvailableUsers;

            // 分页相关数据
            const pageSize = ref(14); // 每页显示14条数据
            const currentPage = ref(1); // 当前页码
            const totalTasks =  ref(initData.totalTasks); // 总任务数量，用于后端分页

            // 筛选相关数据和方法
            const hasActiveFilters = ref(false); // 是否有激活的筛选条件
            
            // 动态生成项目筛选选项
            const projectFilters = computed(() => {
                const projectNames = new Set();
                tasks.value.forEach(task => {
                    if (task.project_name) projectNames.add(task.project_name);
                });
                return Array.from(projectNames).map(name => ({ text: name, value: name }));
            });
            
            // 紧急程度筛选方法
            const filterUrgency = (value, row) => {
                hasActiveFilters.value = true;
                return row.urgency === value;
            };
            
            // 项目筛选方法
            const filterProject = (value, row) => {
                hasActiveFilters.value = true;
                return row.project_name === value;
            };
            
            // 清除所有筛选器
            const clearAllFilters = () => {
                if (tableRef && tableRef.value) {
                    tableRef.value.clearFilter();
                    hasActiveFilters.value = false;
                }
            };

            // 表格引用
            const tableRef = ref(null);

            // 后端分页模式下，直接使用后端返回的当前页数据
            const paginatedTasks = computed(() => {
                return tasks.value;
            });

            // 处理页码变化 - 后端分页模式
            const handlePageChange = (newPage) => {
                currentPage.value = newPage;
                // 如果有抽屉打开，则关闭它
                if (drawer.value.visible) {
                    closeDrawer();
                }
                
                // 根据当前视图类型重新加载数据
                if (currentView.value === 'project' && currentProject.value && currentProject.value.id) {
                    applyFilters();
                } else if (currentView.value === 'completed') {
                    statusFilters();
                } else if (currentView.value === 'received') {
                    statusFilters();
                } else if (currentView.value === 'created' || currentView.value === 'search') {
                    loadViewData(currentView.value);
                }
            };

            // 从pm_members.js导入成员管理功能
            const memberUtils = initializeMemberManagement(projectDialog, loadProjectMembers, ElementPlus);
            
            // 导入成员管理功能
            const addProjectMember = memberUtils.addProjectMember;
            const removeProjectMember = memberUtils.removeProjectMember;
            const handleProjectDialogClosed = memberUtils.handleProjectDialogClosed;
            const saveProjectSettings = memberUtils.saveProjectSettings;
            const showAddProjectDialog = memberUtils.showAddProjectDialog;
            const handleRoleChange = memberUtils.handleRoleChange;
            const getFilteredProjectMembers = memberUtils.getFilteredProjectMembers;
            
            // 从pm_templates.js导入模板管理功能
            const templateUtils = initializeTemplateManagement(Vue, ElementPlus, currentView);
            const templates = templateUtils.templates;
            const templateLoading = templateUtils.templateLoading;
            const templateSearch = templateUtils.templateSearch;
            const filteredTemplates = templateUtils.filteredTemplates;
            const loadTemplates = templateUtils.loadTemplates;
            const showAddTemplateDialog = templateUtils.showAddTemplateDialog;
            const viewTemplate = templateUtils.viewTemplate;
            const deleteTemplate = templateUtils.deleteTemplate;
            const templateEditDialog = templateUtils.templateEditDialog;
            const showEditTemplateDialog = templateUtils.showEditTemplateDialog;
            const closeTemplateEditDialog = templateUtils.closeTemplateEditDialog;
            const submitTemplateEdit = templateUtils.submitTemplateEdit;
            const currentTemplateDetails = templateUtils.currentTemplateDetails;
            const templateTasks = templateUtils.templateTasks;
            const templateTasksLoading = templateUtils.templateTasksLoading;
            const loadTemplateTasks = templateUtils.loadTemplateTasks;
            const selectedTemplateTask = templateUtils.selectedTemplateTask;
            const templateTaskDialog = templateUtils.templateTaskDialog;
            const handleTemplateTaskRowClick = templateUtils.handleTemplateTaskRowClick;
            const showAddTemplateTaskDialog = templateUtils.showAddTemplateTaskDialog;
            const showAddTemplateSubTaskDialog = templateUtils.showAddTemplateSubTaskDialog;
            const showEditTemplateTaskDialog = templateUtils.showEditTemplateTaskDialog;
            const closeTemplateTaskDialog = templateUtils.closeTemplateTaskDialog;
            const submitTemplateTask = templateUtils.submitTemplateTask;
            const deleteTemplateTask = templateUtils.deleteTemplateTask;
            const moveTemplateTask = templateUtils.moveTemplateTask;
            // 添加新模板相关功能
            const addTemplateDialog = templateUtils.addTemplateDialog;
            const closeAddTemplateDialog = templateUtils.closeAddTemplateDialog;
            const submitAddTemplate = templateUtils.submitAddTemplate;
            
            // 从pm_tasks.js导入任务管理功能
            const taskUtils = initializeTaskManagement(
                currentView,
                currentProject,
                searchQuery,
                tasks,
                totalTasks,
                currentPage,
                tableLoading,
                drawer,
                projects,
                ElementPlus
            );
            
            // 导入任务管理功能
            const searchTasks = taskUtils.searchTasks;
            const getRowClass = taskUtils.getRowClass;
            const getCompleteTitle = taskUtils.getCompleteTitle;
            const handleRowClick = taskUtils.handleRowClick;
            const closeDrawer = taskUtils.closeDrawer;
            const sendReply = taskUtils.sendReply;
            const completeTask = taskUtils.completeTask;
            const goToProject = taskUtils.goToProject;
            const getEmptyText = taskUtils.getEmptyText;
            // const getTaskStatusLabel = taskUtils.getTaskStatusLabel;
            const sendDingReply = taskUtils.sendDingReply;

            // 包装selectProject函数，增加筛选器重置功能
            const selectProject = (project) => {
                // 重置筛选器状态
                taskStatusFilter.value = 'all';
                selectedCreateMonth.value = "";
                selectedMonth.value = "";
                projectTaskSearchQuery.value="";
                
                // 重置分页到第一页
                currentPage.value = 1;
                
                // 调用实际的selectProject函数
                taskUtils.selectProject(project);
            };
            // 添加项目搜索和过滤功能
            const projectSearchQuery = ref('');
            
            // 过滤后的项目列表 (用于快速任务项目选择)
            const filteredTaskProjects = computed(() => {
                if (!projectSearchQuery.value || !projects.value) {
                    return projects.value;
                }
                
                const query = projectSearchQuery.value.toLowerCase();
                return projects.value.filter(project => {
                    return project.name.toLowerCase().includes(query);
                });
            });

            // 首页相关数据
            const myTodoTasks = ref([]);
            const myCreatedTasks = ref([]);
            
            // 从pm_home.js导入首页功能
            const homeFeatures = initializeHomeFeatures(Vue, myTodoTasks, myCreatedTasks, ElementPlus);
            
            // 导入首页功能
            const homeLoading = homeFeatures.homeLoading;
            const activeTodoTab = homeFeatures.activeTodoTab;
            const activeCreatedTab = homeFeatures.activeCreatedTab;
            const statsTimeRange = homeFeatures.statsTimeRange;
            const customDateRange = homeFeatures.customDateRange;
            const formattedDate = homeFeatures.formattedDate;
            const greeting = homeFeatures.greeting;
            const getStatsTimeRangeLabel = homeFeatures.getStatsTimeRangeLabel;
            const handleStatsTimeRangeChange = homeFeatures.handleStatsTimeRangeChange;
            const handleCustomDateRangeChange = homeFeatures.handleCustomDateRangeChange;
            const quickTaskName = homeFeatures.quickTaskName;
            const quickTaskDeadline = homeFeatures.quickTaskDeadline;
            const quickTaskProjectId = homeFeatures.quickTaskProjectId;
            const quickTaskUrgency = homeFeatures.quickTaskUrgency;
            const handleQuickTaskDateChange = homeFeatures.handleQuickTaskDateChange;
            const handleQuickTaskProjectChange = homeFeatures.handleQuickTaskProjectChange;
            const handleQuickTaskUrgencyChange = homeFeatures.handleQuickTaskUrgencyChange;
            const focusQuickTaskDate = homeFeatures.focusQuickTaskDate;
            const quickCreatedTaskName = homeFeatures.quickCreatedTaskName;
            const quickCreatedTaskDeadline = homeFeatures.quickCreatedTaskDeadline;
            const quickCreatedTaskProjectId = homeFeatures.quickCreatedTaskProjectId;
            const quickCreatedTaskAssignees = homeFeatures.quickCreatedTaskAssignees;
            const quickCreatedTaskUrgency = homeFeatures.quickCreatedTaskUrgency;
            const availableAssignees = homeFeatures.availableAssignees;
            const handleQuickCreatedTaskDateChange = homeFeatures.handleQuickCreatedTaskDateChange;
            const handleQuickCreatedTaskProjectChange = homeFeatures.handleQuickCreatedTaskProjectChange;
            const handleQuickCreatedTaskAssigneeChange = homeFeatures.handleQuickCreatedTaskAssigneeChange;
            const handleQuickCreatedTaskUrgencyChange = homeFeatures.handleQuickCreatedTaskUrgencyChange;
            const handleSelectAllQuickTaskAssignees = homeFeatures.handleSelectAllQuickTaskAssignees;
            const focusQuickCreatedTaskDate = homeFeatures.focusQuickCreatedTaskDate;
            const focusStats = homeFeatures.focusStats;
            const statsData = homeFeatures.statsData;
            const filteredTodoTasks = homeFeatures.filteredTodoTasks;
            const filteredCreatedTasks = homeFeatures.filteredCreatedTasks;
            const filteredTodoStats = homeFeatures.filteredTodoStats;
            const formatDate = homeFeatures.formatDate;
            
            // 初始化快速任务项目
            homeFeatures.initQuickTaskProject(projects.value);
            
            // 初始化"我的要求"任务的可选负责人
            if (projects.value && projects.value.length > 0 && projects.value[0].id) {
                homeFeatures.loadProjectAssignees(projects.value[0].id);
            }
            
            // 快速创建任务方法
            const createQuickTask = () => {
                homeFeatures.createQuickTask(currentProject.value, projects.value, loadHomeData);
            };
            
            // 快速创建"我的要求"任务方法
            const createQuickCreatedTask = () => {
                homeFeatures.createQuickCreatedTask(currentProject.value, projects.value, loadHomeData);
            };
            
            // 处理任务标签页切换
            const handleTabChange = (type, tabName) => {
                homeFeatures.handleTabChange(type, tabName);
                
                // 当标签页切换时，重新渲染图表
                setTimeout(() => {
                    renderTodoTimeDistribution();
                    renderTodoDailyTracking();
                }, 100);
            };
            
            // 从pm_charts.js导入图表功能
            const chartUtils = initializeCharts(myTodoTasks, formatDate);
            
            // 导入图表功能
            const renderTodoTimeDistribution = chartUtils.renderTodoTimeDistribution;
            const renderTodoDailyTracking = chartUtils.renderTodoDailyTracking;
            // 导入项目统计图表函数
            const renderTaskStatusPieChart = chartUtils.renderTaskStatusPieChart;
            const renderWeeklyTaskCompletionChart = chartUtils.renderWeeklyTaskCompletionChart;
            const renderTeamMemberTaskChart = chartUtils.renderTeamMemberTaskChart;
            const renderTaskPriorityChart = chartUtils.renderTaskPriorityChart;
            const renderProjectStatCharts = chartUtils.renderProjectStatCharts;
            
            // 从pm_task_handler.js导入任务添加功能
            const taskAddUtils = initializeTaskHandler(currentProject, tasks, currentPage,  totalTasks,Vue, ElementPlus, drawer);
            
            // 导入任务添加功能
            const taskDialog = taskAddUtils.taskDialog;
            const showAddTaskDialog = taskAddUtils.showAddTaskDialog;
            const showAddHistoricalTaskDialog = taskAddUtils.showAddHistoricalTaskDialog;
            const showAddSubTaskDialog = taskAddUtils.showAddSubTaskDialog;
            const showEditTaskDialog = taskAddUtils.showEditTaskDialog;
            const submitAddTask = taskAddUtils.submitAddTask;
            const closeTaskDialog = taskAddUtils.closeTaskDialog;
            const disablePastDates = taskAddUtils.disablePastDates;
            
            // 修改初始化任务表单函数，添加ccUsers字段
            const initTaskForm = () => {
                return {
                    name: '',
                    description: '',
                    deadline: '',
                    urgency: '一般',
                    assignees: [],
                    isImmediate: true, // 默认开启单人单任务
                    ccUsers: [] // 添加抄送人字段
                };
            };
            
            // 覆盖taskDialog中的初始表单方法
            if (taskDialog.value && taskDialog.value.form) {
                taskDialog.value.form = initTaskForm();
            }
            
            // 表单引用
            const taskFormRef = ref(null);
            const taskStatus = ref('all'); // 默认为"全部"
            const projectTaskSearchQuery = ref('');
            
            // 负责人全选功能
            const selectAllAssignees = ref(false);
            
            // 处理全选负责人
            const handleSelectAllAssignees = () => {
                // 当前是全选状态，则取消全选
                if (selectAllAssignees.value) {
                    taskDialog.value.form.assignees = [];
                    selectAllAssignees.value = false;
                } 
                // 当前未全选，则全选
                else {
                    taskDialog.value.form.assignees = taskDialog.value.availableAssignees.map(assignee => assignee.value);
                    selectAllAssignees.value = true;
                }
            };
            
            // 组织架构树对话框状态
            const orgTreeDialog = ref({
                visible: false,
                loading: false,
                searchKeyword: '',
                currentDept: null,
                currentPath: [], // 当前导航路径
                childDepts: [], // 当前部门的子部门
                deptUsers: [], // 当前部门的用户
                searchDepts: [], // 搜索结果-部门
                searchUsers: [], // 搜索结果-用户
                selectAllChecked: false, // 全选状态
                selectedDepts: new Set(), // 已选择的部门ID集合
                dialogMode: 'cc' // 对话框模式：'cc'表示抄送人，'assignee'表示负责人
            });
            
            // 选中的用户Map，用于快速查找
            const selectedUsersMap = ref(new Map());
            
            // 初始化已选用户Map
            const initSelectedUsersMap = () => {
                selectedUsersMap.value.clear();
                if (taskDialog.value && taskDialog.value.form) {
                    if (orgTreeDialog.value.dialogMode === 'cc' && taskDialog.value.form.ccUsers) {
                        taskDialog.value.form.ccUsers.forEach(user => {
                            selectedUsersMap.value.set(user.userid, user);
                        });
                    } else if (orgTreeDialog.value.dialogMode === 'assignee' && taskDialog.value.form.assignees) {
                        // 为负责人模式初始化选中状态
                        taskDialog.value.availableAssignees.forEach(assignee => {
                            if (taskDialog.value.form.assignees.includes(assignee.value)) {
                                selectedUsersMap.value.set(assignee.value, {
                                    userid: assignee.value,
                                    name: assignee.label,
                                    department: assignee.department
                                });
                            }
                        });
                    }
                }
            };
            
            // 保存打开对话框前的抄送人状态，用于取消时恢复
            const originalCcUsers = ref([]);
            
            // 保存打开对话框前的负责人状态，用于取消时恢复
            const originalAssignees = ref([]);
            
            // 显示组织架构树对话框 - 抄送人模式
            const showOrgTreeDialog = () => {
                // 设置对话框模式为抄送人
                orgTreeDialog.value.dialogMode = 'cc';
                
                // 保存当前抄送人状态，以便取消时恢复
                originalCcUsers.value = JSON.parse(JSON.stringify(taskDialog.value.form.ccUsers || []));
                
                orgTreeDialog.value.visible = true;
                orgTreeDialog.value.searchKeyword = '';
                orgTreeDialog.value.selectAllChecked = false;
                orgTreeDialog.value.selectedDepts = new Set(); // 重置已选部门
                
                // 初始化已选用户Map
                initSelectedUsersMap();
                
                // 加载顶级部门
                loadRootDepartment();
            };
            
            // 显示组织架构树对话框 - 负责人模式
            const showAssigneeOrgTreeDialog = () => {
                // 设置对话框模式为负责人
                orgTreeDialog.value.dialogMode = 'assignee';
                
                // 保存当前负责人状态，以便取消时恢复
                originalAssignees.value = [...(taskDialog.value.form.assignees || [])];
                
                orgTreeDialog.value.visible = true;
                orgTreeDialog.value.searchKeyword = '';
                orgTreeDialog.value.selectAllChecked = false;
                orgTreeDialog.value.selectedDepts = new Set(); // 重置已选部门
                
                // 初始化已选用户Map
                initSelectedUsersMap();
                
                // 加载顶级部门
                loadRootDepartment();
            };
            
            // 加载根部门
            const loadRootDepartment = async () => {
                orgTreeDialog.value.loading = true;
                try {
                    // 创建一个虚拟的总公司节点
                    const rootDept = {
                        dept_id: 1,
                        name: '总公司',
                        parent_id: 0
                    };
                    
                    // 设置当前部门和路径
                    orgTreeDialog.value.currentDept = rootDept;
                    orgTreeDialog.value.currentPath = [rootDept];
                    
                    // 获取parentId=1的子部门
                    const response = await fetch('/api/organization/departments?parentId=1');
                    const result = await response.json();
                    
                    if (result.code === 0) {
                        // 设置子部门
                        orgTreeDialog.value.childDepts = result.data || [];
                        
                        // 加载总公司的用户
                        await loadDeptUsers(1);
                    } else {
                        throw new Error(result.message || '获取部门数据失败');
                    }
                } catch (error) {
                    console.error('加载组织架构失败:', error);
                    ElementPlus.ElMessage.error('加载组织架构失败，请稍后重试');
                } finally {
                    orgTreeDialog.value.loading = false;
                }
            };
            
            // 加载部门用户
            const loadDeptUsers = async (deptId) => {
                if (!deptId) return;
                
                orgTreeDialog.value.loading = true;
                orgTreeDialog.value.deptUsers = [];
                
                try {
                    // 获取当前用户ID（提前获取，确保在任何情况下都能获取到）
                    const currentUserId = getCurrentUserId();
                    console.log('加载部门用户 - 当前用户ID:', currentUserId);
                    
                    // 获取部门用户
                    const userResponse = await fetch(`/api/organization/users?deptId=${deptId}`);
                    
                    // 处理用户响应
                    const userResult = await userResponse.json();
                    if (userResult.code === 0) {
                        // 标记当前用户不可选
                        orgTreeDialog.value.deptUsers = (userResult.data || []).map(user => {
                            const isCurrentUser = String(user.userid) === String(currentUserId);
                            return {
                                ...user,
                                isCurrentUser: isCurrentUser,
                                disabled: isCurrentUser && orgTreeDialog.value.dialogMode !== 'cc'
                            };
                        });
                    } else {
                        throw new Error(userResult.message || '获取部门用户失败');
                    }
                } catch (error) {
                    console.error('加载部门用户失败:', error);
                    ElementPlus.ElMessage.error('加载部门用户失败，请稍后重试');
                } finally {
                    orgTreeDialog.value.loading = false;
                }
            };
            
            // 加载部门子部门和用户
            const loadDeptChildren = async (deptId) => {
                if (!deptId) return;
                
                orgTreeDialog.value.loading = true;
                orgTreeDialog.value.childDepts = [];
                orgTreeDialog.value.deptUsers = [];
                
                try {
                    // 获取当前用户ID（提前获取，确保在任何情况下都能获取到）
                    const currentUserId = getCurrentUserId();
                    console.log('加载部门子部门和用户 - 当前用户ID:', currentUserId);
                    
                    // 并行请求子部门和用户
                    const [deptResponse, userResponse] = await Promise.all([
                        fetch(`/api/organization/departments?parentId=${deptId}`),
                        fetch(`/api/organization/users?deptId=${deptId}`)
                    ]);
                    
                    // 处理子部门响应
                    const deptResult = await deptResponse.json();
                    if (deptResult.code === 0) {
                        orgTreeDialog.value.childDepts = deptResult.data || [];
                    } else {
                        throw new Error(deptResult.message || '获取子部门失败');
                    }
                    
                    // 处理用户响应
                    const userResult = await userResponse.json();
                    if (userResult.code === 0) {
                        // 标记当前用户不可选
                        orgTreeDialog.value.deptUsers = (userResult.data || []).map(user => {
                            const isCurrentUser = String(user.userid) === String(currentUserId);
                            return {
                                ...user,
                                isCurrentUser: isCurrentUser,
                                disabled: isCurrentUser && orgTreeDialog.value.dialogMode !== 'cc'
                            };
                        });
                    } else {
                        throw new Error(userResult.message || '获取部门用户失败');
                    }
                } catch (error) {
                    console.error('加载部门数据失败:', error);
                    ElementPlus.ElMessage.error('加载部门数据失败，请稍后重试');
                } finally {
                    orgTreeDialog.value.loading = false;
                }
            };
            
            // 处理部门点击
            const handleDeptClick = async (dept) => {
                if (!dept || !dept.dept_id) return;
                
                // 更新当前部门
                orgTreeDialog.value.currentDept = dept;
                
                // 更新导航路径
                // 如果是从搜索结果点击的部门，需要重新构建路径
                if (orgTreeDialog.value.searchKeyword) {
                    // 清除搜索关键词，回到正常浏览模式
                    orgTreeDialog.value.searchKeyword = '';
                    
                    // 构建新的路径，确保包含从总公司开始的完整路径
                    await buildDeptPath(dept.dept_id);
                } else {
                    // 正常浏览模式下，直接添加到路径末尾
                    orgTreeDialog.value.currentPath.push(dept);
                }
                
                // 重置全选状态
                orgTreeDialog.value.selectAllChecked = false;
                
                // 加载子部门和用户
                await loadDeptChildren(dept.dept_id);
            };
            
            // 构建部门路径
            const buildDeptPath = async (deptId) => {
                if (!deptId) return;
                
                try {
                    const response = await fetch(`/api/organization/dept_path?deptId=${deptId}`);
                    const result = await response.json();
                    
                    if (result.code === 0 && result.data && result.data.length > 0) {
                        // 确保路径包含总公司
                        const pathIncludesRoot = result.data.some(dept => dept.dept_id === 1);
                        
                        if (!pathIncludesRoot) {
                            // 如果路径中没有总公司，添加虚拟的总公司节点到路径开头
                            const rootDept = {
                                dept_id: 1,
                                name: '总公司',
                                parent_id: 0
                            };
                            orgTreeDialog.value.currentPath = [rootDept, ...result.data];
                        } else {
                            orgTreeDialog.value.currentPath = result.data;
                        }
                    } else {
                        // 如果获取路径失败，创建包含总公司和当前部门的基本路径
                        const rootDept = {
                            dept_id: 1,
                            name: '总公司',
                            parent_id: 0
                        };
                        orgTreeDialog.value.currentPath = [rootDept, orgTreeDialog.value.currentDept];
                    }
                } catch (error) {
                    console.error('构建部门路径失败:', error);
                    // 设置一个包含总公司的基本路径
                    const rootDept = {
                        dept_id: 1,
                        name: '总公司',
                        parent_id: 0
                    };
                    orgTreeDialog.value.currentPath = [rootDept, orgTreeDialog.value.currentDept];
                }
            };
            
            // 导航到指定部门
            const navigateToDept = async (dept, index) => {
                if (!dept || !dept.dept_id) return;
                
                // 更新当前部门
                orgTreeDialog.value.currentDept = dept;
                
                // 截断路径到当前点击的位置
                orgTreeDialog.value.currentPath = orgTreeDialog.value.currentPath.slice(0, index + 1);
                
                // 重置全选状态
                orgTreeDialog.value.selectAllChecked = false;
                
                // 加载子部门和用户
                await loadDeptChildren(dept.dept_id);
            };
            
            // 搜索组织成员
            const searchOrgMembers = debounce(async () => {
                const keyword = orgTreeDialog.value.searchKeyword.trim();
                
                // 如果关键词为空，恢复到当前部门的浏览
                if (!keyword) {
                    if (orgTreeDialog.value.currentDept) {
                        await loadDeptChildren(orgTreeDialog.value.currentDept.dept_id);
                    }
                    return;
                }
                
                orgTreeDialog.value.loading = true;
                orgTreeDialog.value.searchDepts = [];
                orgTreeDialog.value.searchUsers = [];
                
                try {
                    // 获取当前用户ID（提前获取，确保在任何情况下都能获取到）
                    const currentUserId = getCurrentUserId();
                    console.log('当前用户ID:', currentUserId);
                    
                    const response = await fetch(`/api/organization/search?keyword=${encodeURIComponent(keyword)}`);
                    const result = await response.json();
                    
                    if (result.code === 0 && result.data) {
                        orgTreeDialog.value.searchDepts = result.data.departments || [];
                        
                        // 标记当前用户不可选
                        orgTreeDialog.value.searchUsers = (result.data.users || []).map(user => {
                            const isCurrentUser = String(user.userid) === String(currentUserId);
                            return {
                                ...user,
                                isCurrentUser: isCurrentUser,
                                disabled: isCurrentUser && orgTreeDialog.value.dialogMode !== 'cc'
                            };
                        });
                    } else {
                        throw new Error(result.message || '搜索失败');
                    }
                } catch (error) {
                    console.error('搜索组织架构失败:', error);
                    ElementPlus.ElMessage.error('搜索失败，请稍后重试');
                } finally {
                    orgTreeDialog.value.loading = false;
                }
            }, 300);
            
            // 处理全选当前部门
            const handleSelectAllDept = async (checked) => {
                if (!orgTreeDialog.value.currentDept) return;
                
                // 当前部门ID
                const deptId = orgTreeDialog.value.currentDept.dept_id;
                
                if (checked) {
                    // 将当前部门添加到已选部门列表
                    orgTreeDialog.value.selectedDepts.add(deptId);
                    
                    // 全选当前部门及其所有子部门的用户
                    await selectDeptWithAllUsers(deptId);
                } else {
                    // 从已选部门列表中移除当前部门
                    orgTreeDialog.value.selectedDepts.delete(deptId);
                    
                    // 取消选择当前部门及其所有子部门的用户
                    await unselectDeptWithAllUsers(deptId);
                }
            };
            
            // 处理选择部门
            const handleSelectDept = async (dept, checked) => {
                if (!dept || !dept.dept_id) return;
                
                if (checked) {
                    // 添加部门到已选列表
                    orgTreeDialog.value.selectedDepts.add(dept.dept_id);
                    
                    // 加载部门及其子部门的所有用户
                    await selectDeptWithAllUsers(dept.dept_id);
                } else {
                    // 从已选列表中移除部门
                    orgTreeDialog.value.selectedDepts.delete(dept.dept_id);
                    
                    // 移除该部门及其子部门的所有用户
                    await unselectDeptWithAllUsers(dept.dept_id);
                }
                
                // 更新全选状态
                updateDeptSelectAllState();
            };
            
            // 加载并选择部门及其所有子部门的用户
            const selectDeptWithAllUsers = async (deptId) => {
                if (!deptId) return;
                
                try {
                    // 显示加载状态
                    orgTreeDialog.value.loading = true;
                    
                    // 获取部门及其所有子部门的用户
                    const response = await fetch(`/api/organization/dept_all_users?deptId=${deptId}`);
                    const result = await response.json();
                    
                    if (result.code === 0 && result.data) {
                        // 添加所有用户到已选列表
                        result.data.forEach(user => {
                            if (!selectedUsersMap.value.has(user.userid)) {
                                const newUser = {
                                    userid: user.userid,
                                    name: user.name
                                };
                                
                                if (orgTreeDialog.value.dialogMode === 'cc') {
                                    // 抄送人模式
                                    taskDialog.value.form.ccUsers.push(newUser);
                                } else {
                                    // 负责人模式 - 检查是否在可选负责人列表中
                                    const assignee = taskDialog.value.availableAssignees.find(a => a.value === user.userid);
                                    if (assignee && !taskDialog.value.form.assignees.includes(user.userid)) {
                                        taskDialog.value.form.assignees.push(user.userid);
                                    }
                                }
                                
                                selectedUsersMap.value.set(user.userid, newUser);
                            }
                        });
                        
                        // 如果当前正在查看这个部门，更新全选状态
                        if (orgTreeDialog.value.currentDept && 
                            orgTreeDialog.value.currentDept.dept_id === deptId) {
                            orgTreeDialog.value.selectAllChecked = true;
                        }
                    } else {
                        throw new Error(result.message || '获取部门用户失败');
                    }
                } catch (error) {
                    console.error('选择部门用户失败:', error);
                    ElementPlus.ElMessage.error('选择部门用户失败，请稍后重试');
                } finally {
                    orgTreeDialog.value.loading = false;
                }
            };
            
            // 取消选择部门及其所有子部门的用户
            const unselectDeptWithAllUsers = async (deptId) => {
                if (!deptId) return;
                
                try {
                    // 显示加载状态
                    orgTreeDialog.value.loading = true;
                    
                    // 获取部门及其所有子部门的用户
                    const response = await fetch(`/api/organization/dept_all_users?deptId=${deptId}`);
                    const result = await response.json();
                    
                    if (result.code === 0 && result.data) {
                        // 从已选列表中移除所有用户
                        result.data.forEach(user => {
                            if (selectedUsersMap.value.has(user.userid)) {
                                if (orgTreeDialog.value.dialogMode === 'cc') {
                                    // 抄送人模式
                                    const index = taskDialog.value.form.ccUsers.findIndex(u => u.userid === user.userid);
                                    if (index !== -1) {
                                        taskDialog.value.form.ccUsers.splice(index, 1);
                                    }
                                } else {
                                    // 负责人模式
                                    const index = taskDialog.value.form.assignees.indexOf(user.userid);
                                    if (index !== -1) {
                                        taskDialog.value.form.assignees.splice(index, 1);
                                    }
                                }
                                
                                selectedUsersMap.value.delete(user.userid);
                            }
                        });
                        
                        // 如果当前正在查看这个部门，更新全选状态
                        if (orgTreeDialog.value.currentDept && 
                            orgTreeDialog.value.currentDept.dept_id === deptId) {
                            orgTreeDialog.value.selectAllChecked = false;
                        }
                    } else {
                        throw new Error(result.message || '获取部门用户失败');
                    }
                } catch (error) {
                    console.error('取消选择部门用户失败:', error);
                    ElementPlus.ElMessage.error('取消选择部门用户失败，请稍后重试');
                } finally {
                    orgTreeDialog.value.loading = false;
                }
            };
            
            // 更新部门全选状态
            const updateDeptSelectAllState = () => {
                // 检查当前部门是否被选中
                if (orgTreeDialog.value.currentDept) {
                    const deptId = orgTreeDialog.value.currentDept.dept_id;
                    orgTreeDialog.value.selectAllChecked = orgTreeDialog.value.selectedDepts.has(deptId);
                }
            };
            
            // 添加或移除抄送人/负责人
            const toggleCcUser = (user) => {
                // 检查是否是当前用户且不是抄送人模式
                const currentUserId = getCurrentUserId();
                console.log('toggleCcUser - 当前用户ID:', currentUserId, '选择的用户ID:', user.userid);
                
                // 确保进行字符串比较，因为ID可能是数字或字符串
                if (String(user.userid) === String(currentUserId) && orgTreeDialog.value.dialogMode !== 'cc') {
                    ElementPlus.ElMessage.warning('不能选择自己作为任务负责人');
                    return;
                }
                
                // 如果用户被标记为禁用，不允许选择
                if (user.disabled) {
                    return;
                }
                
                const isSelected = selectedUsersMap.value.has(user.userid);
                
                if (!isSelected) {
                    // 添加用户
                    const newUser = {
                        userid: user.userid,
                        name: user.name
                    };
                    
                    if (orgTreeDialog.value.dialogMode === 'cc') {
                        // 抄送人模式
                        taskDialog.value.form.ccUsers.push(newUser);
                    } else {
                        // 负责人模式 - 直接添加到assignees数组
                        if (!taskDialog.value.form.assignees.includes(user.userid)) {
                            taskDialog.value.form.assignees.push(user.userid);
                        }
                    }
                    
                    selectedUsersMap.value.set(user.userid, newUser);
                } else {
                    // 移除用户
                    removeCcUser(user);
                }
                
                // 更新全选状态
                updateSelectAllState();
            };
            
            // 更新全选状态
            const updateSelectAllState = () => {
                // 只在正常浏览模式且有用户的情况下更新全选状态
                if (!orgTreeDialog.value.searchKeyword && orgTreeDialog.value.deptUsers.length > 0) {
                    // 检查当前部门的所有用户是否都被选中
                    const allSelected = orgTreeDialog.value.deptUsers.every(user => 
                        selectedUsersMap.value.has(user.userid)
                    );
                    orgTreeDialog.value.selectAllChecked = allSelected;
                }
            };
            
            // 检查用户是否已选择
            const isUserSelected = (user) => {
                // 如果是当前用户且不是抄送人模式，显示为不可选
                const currentUserId = getCurrentUserId();
                if (user.userid === currentUserId && orgTreeDialog.value.dialogMode !== 'cc' && user.disabled) {
                    return false;
                }
                return selectedUsersMap.value.has(user.userid);
            };
            
            // 检查部门是否已选择
            const isDeptSelected = (dept) => {
                return orgTreeDialog.value.selectedDepts.has(dept.dept_id);
            };
            
            // 移除已选抄送人/负责人
            const removeCcUser = (user) => {
                console.log("移除用户:", user, "当前模式:", orgTreeDialog.value.dialogMode);
                
                // 确保在任何情况下都有有效的userid
                const userId = user?.userid || user;
                
                if (orgTreeDialog.value.dialogMode === 'cc' || !orgTreeDialog.value.visible) {
                    // 抄送人模式或在对话框外（主任务表单）
                    const index = taskDialog.value.form.ccUsers.findIndex(u => String(u.userid) === String(userId));
                    if (index !== -1) {
                        console.log("移除抄送人:", taskDialog.value.form.ccUsers[index].name);
                        taskDialog.value.form.ccUsers.splice(index, 1);
                    }
                } else {
                    // 负责人模式
                    const index = taskDialog.value.form.assignees.indexOf(userId);
                    if (index !== -1) {
                        console.log("移除负责人:", taskDialog.value.availableAssignees.find(a => a.value === userId)?.label);
                        taskDialog.value.form.assignees.splice(index, 1);
                    }
                }
                
                if (typeof userId === 'string' || typeof userId === 'number') {
                    selectedUsersMap.value.delete(userId);
                } else if (userId && userId.userid) {
                    selectedUsersMap.value.delete(userId.userid);
                }
                
                // 更新全选状态
                updateSelectAllState();
            };
            
            // 清空所有已选用户
            const clearSelectedUsers = () => {
                if (orgTreeDialog.value.dialogMode === 'cc') {
                    // 抄送人模式
                    taskDialog.value.form.ccUsers = [];
                } else {
                    // 负责人模式
                    taskDialog.value.form.assignees = [];
                    selectAllAssignees.value = false;
                }
                
                selectedUsersMap.value.clear();
                orgTreeDialog.value.selectAllChecked = false;
                orgTreeDialog.value.selectedDepts = new Set(); // 清空已选部门
            };
            
            // 取消选择，恢复到打开对话框前的状态
            const cancelCcUsers = () => {
                // 根据对话框模式恢复原始状态
                if (orgTreeDialog.value.dialogMode === 'cc') {
                    // 恢复原始抄送人状态
                    taskDialog.value.form.ccUsers = JSON.parse(JSON.stringify(originalCcUsers.value));
                } else {
                    // 恢复原始负责人状态
                    taskDialog.value.form.assignees = [...originalAssignees.value];
                    // 更新全选状态
                    if (taskDialog.value.availableAssignees.length > 0) {
                        selectAllAssignees.value = taskDialog.value.form.assignees.length === taskDialog.value.availableAssignees.length;
                    }
                }
                
                // 关闭对话框
                orgTreeDialog.value.visible = false;
            };
            
            // 确认选择的抄送人/负责人
            const confirmCcUsers = async () => {
                // 先关闭对话框，避免UI冻结
                const dialogMode = orgTreeDialog.value.dialogMode; // 保存当前模式
                orgTreeDialog.value.visible = false;
                
                // 如果是负责人模式，更新全选状态并重新获取领导人
                if (dialogMode === 'assignee' && taskDialog.value.availableAssignees.length > 0) {
                    selectAllAssignees.value = taskDialog.value.form.assignees.length === taskDialog.value.availableAssignees.length;
                    
                    // 根据选择的负责人，重新获取对应的领导并更新抄送人
                    try {
                        // 显示加载状态
                        const loadingInstance = ElementPlus.ElLoading.service({
                            lock: true,
                            text: '正在获取负责人领导...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        // 获取所有负责人的领导
                        const assigneeIds = [...taskDialog.value.form.assignees]; // 复制负责人ID数组
                        if (assigneeIds.length === 0) {
                            loadingInstance.close();
                            return; // 没有负责人，不需要获取领导
                        }
                        
                        const queryParams = assigneeIds.map(id => `assignee_id=${id}`).join('&');
                        const response = await fetch(`/api/assignee_leaders?${queryParams}`);
                        
                        if (!response.ok) {
                            const errorData = await response.json();
                            throw new Error(errorData.message || '获取负责人领导失败');
                        }
                        
                        const data = await response.json();
                        
                        if (data.success && data.leaders) {
                            // 创建新的抄送人数组
                            let newCcUsers = [];
                            
                            // 复制原有抄送人
                            if (taskDialog.value.form.ccUsers && taskDialog.value.form.ccUsers.length > 0) {
                                // 深拷贝以确保完全独立的对象
                                newCcUsers = JSON.parse(JSON.stringify(taskDialog.value.form.ccUsers));
                            }
                            
                            // 将领导添加到抄送人列表中
                            if (data.leaders.length > 0) {
                                data.leaders.forEach(leader => {
                                    // 检查是否已经存在该领导
                                    const exists = newCcUsers.some(user => String(user.userid) === String(leader.userid));
                                    if (!exists) {
                                        // 添加一个标记，表示这是自动添加的领导
                                        newCcUsers.push({
                                            userid: leader.userid,
                                            name: leader.name,
                                            isLeader: true
                                        });
                                    }
                                });
                            }
                            
                            // 使用Vue的响应式API替换数组
                            setTimeout(() => {
                                // 在下一个事件循环中更新，确保对话框已完全关闭
                                taskDialog.value.form.ccUsers = newCcUsers;
                                console.log("抄送人列表已更新:", taskDialog.value.form.ccUsers);
                            }, 100);
                        }
                        
                        loadingInstance.close();
                    } catch (error) {
                        console.error('获取负责人领导失败:', error);
                        ElementPlus.ElMessage.warning('获取负责人领导失败，请稍后重试');
                    }
                }
            };

            // 防抖函数
            function debounce(fn, delay) {
                let timer = null;
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(timer);
                    timer = setTimeout(() => {
                        fn.apply(context, args);
                    }, delay);
                };
            }
            
            // 通用筛选函数，整合所有筛选条件
            const completedSearchQuery = ref('');
            const receivedSearchQuery = ref('');  // 添加抄送任务的搜索查询
            const receivedTaskStatus = ref('all');  // 添加抄送任务的状态筛选
            const receivedTaskType = ref('');  // 添加任务类型筛选：assigned/created/cc，空字符串表示未选择特定类型
            const receivedSelectedMonth = ref(null);  // 添加抄送任务的月份筛选
            
            // 处理抄送任务月份变化
            const handleReceivedMonthChange = (value) => {
                receivedSelectedMonth.value = value;
                 currentPage.value = 1;
                statusFilters(); // 触发筛选
            };
            
            // 切换任务类型筛选
            const toggleTaskTypeFilter = (type) => {
                // 如果当前已经选中该类型，则取消选择（恢复默认状态）
                if (receivedTaskType.value === type) {
                    receivedTaskType.value = ''; // 空字符串表示未选择任何特定类型
                } else {
                    // 否则选择该类型
                    receivedTaskType.value = type;
                }
                 currentPage.value = 1;
                // 触发筛选
                statusFilters();
            };
            
            const statusFilters = () => {
                // 更新筛选状态值
                // taskStatus.value = value;
                if (currentView.value !== 'completed' && currentView.value !== 'received') {
                    return;
                }

                // 显示加载状态
                tableLoading.value = true;
                tasks.value = [];

                // 移动端特殊处理：立即显示加载提示
                if (window.innerWidth <= 1023 && currentView.value === 'received') {
                    // 触发移动端加载提示显示
                    setTimeout(() => {
                        if (window.MobileReceivedUtils && window.MobileReceivedUtils.showLoadingHint) {
                            window.MobileReceivedUtils.showLoadingHint();
                        }
                    }, 50);
                }

                // 构建API URL
                let url = '';
                if (currentView.value === 'completed') {
                    url = `/projectmanagement/api/view?view_type=completed`;
                    
                    // 添加分页参数
                    url += `&page=${currentPage.value}&page_size=${pageSize.value}`;
                    
                    // 添加任务状态筛选参数
                    if (taskStatus.value && taskStatus.value !== 'all') {
                        url += `&status=${taskStatus.value}`;
                    }
                    // 添加搜索参数
                    if (completedSearchQuery.value && completedSearchQuery.value.trim() !== '') {
                        url += `&search_term=${encodeURIComponent(completedSearchQuery.value.trim())}`;
                    }
                } else if (currentView.value === 'received') {
                    url = `/projectmanagement/api/cc_tasks`;
                    
                    // 添加参数标记
                    let hasParam = false;
                    
                    // 对于视图类型的API，使用 & 连接参数，对于其他API使用 ? 开始
                    const connector = url.includes('?') ? '&' : '?';
                    
                    // 添加分页参数
                    url += `${hasParam ? '&' : connector}page=${currentPage.value}&page_size=${pageSize.value}`;
                    hasParam = true;
                    
                    // 添加任务状态筛选参数
                    if (receivedTaskStatus.value && receivedTaskStatus.value !== 'all') {
                        url += `&status=${receivedTaskStatus.value}`;
                    }
                    
                    // 添加月份筛选参数
                    if (receivedSelectedMonth.value) {
                        url += `&month=${receivedSelectedMonth.value}`;
                    }
                    
                    // 添加搜索参数
                    if (receivedSearchQuery.value && receivedSearchQuery.value.trim() !== '') {
                        url += `&search_term=${encodeURIComponent(receivedSearchQuery.value.trim())}`;
                    }
                    
                    // 添加任务类型筛选参数
                    if (receivedTaskType.value) {
                        url += `&task_type=${receivedTaskType.value}`;
                    }
                }

                console.log("筛选URL:", url); // 添加调试日志

                // 获取任务数据
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取任务失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        tableLoading.value = false;

                        // 移动端特殊处理：移除加载提示
                        if (window.innerWidth <= 1023 && currentView.value === 'received') {
                            if (window.MobileReceivedUtils && window.MobileReceivedUtils.removeLoadingHint) {
                                window.MobileReceivedUtils.removeLoadingHint();
                            }
                        }

                        if (data.success) {
                            tasks.value = data.tasks || [];
                            console.log(`获取到 ${tasks.value.length} 个任务`); // 添加调试日志

                            // 更新总任务数量 - 后端分页
                            if (data.pagination && typeof data.pagination.total !== 'undefined') {
                                totalTasks.value = data.pagination.total;
                            } else {
                                // 兼容旧接口，如果没有返回分页信息，使用任务数组长度
                                totalTasks.value = data.total_count || tasks.value.length;
                            }

                            // 清除任务抽屉
                            if (drawer.value.visible) {
                                closeDrawer();
                            }
                        } else {
                            throw new Error(data.message || '获取任务失败');
                        }
                    })
                    .catch(error => {
                        tableLoading.value = false;

                        // 移动端特殊处理：移除加载提示
                        if (window.innerWidth <= 1023 && currentView.value === 'received') {
                            if (window.MobileReceivedUtils && window.MobileReceivedUtils.removeLoadingHint) {
                                window.MobileReceivedUtils.removeLoadingHint();
                            }
                        }

                        ElementPlus.ElMessage({
                            message: error.message,
                            type: 'error'
                        });
                    });
            };

            // 添加月份选择器相关状态
            const selectedMonth = ref(null);
            const selectedCreateMonth = ref(null);
            const taskStatusFilter = ref('all'); // 默认为"全部"

            // 通用筛选函数，整合所有筛选条件
            const applyFilters = () => {
                if (currentView.value !== 'project' || !currentProject.value || !currentProject.value.id) {
                    return;
                }

                // 显示加载状态
                tableLoading.value = true;
                tasks.value = [];

                // 构建API URL
                let url = `/projectmanagement/api/project_tasks?project_id=${currentProject.value.id}`;
                
                // 添加分页参数
                url += `&page=${currentPage.value}&page_size=${pageSize.value}`;
                
                // 添加创建日期筛选参数
                if (selectedCreateMonth.value) {
                    url += `&create_month=${selectedCreateMonth.value}`;
                }
                
                // 添加截至日期筛选参数
                if (selectedMonth.value) {
                    url += `&month=${selectedMonth.value}`;
                }
                
                // 添加任务状态筛选参数
                if (taskStatusFilter.value && taskStatusFilter.value !== 'all') {
                    url += `&status=${taskStatusFilter.value}`;
                }

                // 添加搜索词参数
                if (projectTaskSearchQuery.value && projectTaskSearchQuery.value.trim() !== '') {
                    url += `&search_term=${encodeURIComponent(projectTaskSearchQuery.value.trim())}`;
                }

                // 获取任务数据
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取项目任务失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        tableLoading.value = false;
                        if (data.success) {
                            tasks.value = data.tasks || [];
                            
                            // 更新总任务数量 - 后端分页
                            if (data.pagination && typeof data.pagination.total !== 'undefined') {
                                totalTasks.value = data.pagination.total;
                            } else {
                                // 兼容旧接口，如果没有返回分页信息，使用任务数组长度
                                totalTasks.value = tasks.value.length;
                            }
                            
                            // 清除任务抽屉
                            if (drawer.value.visible) {
                                closeDrawer();
                            }
                        } else {
                            throw new Error(data.message || '获取项目任务失败');
                        }
                    })
                    .catch(error => {
                        tableLoading.value = false;
                        ElementPlus.ElMessage({
                            message: error.message,
                            type: 'error'
                        });
                    });
            };

            const handleProjectTaskSearch = () => {
                applyFilters();
            };

            // 处理任务状态筛选变化
            const handleTaskStatusChange = (value) => {
                // 更新筛选状态值
                taskStatusFilter.value = value;
                currentPage.value = 1;
                // 调用通用筛选函数
                applyFilters();
            };

            // 处理月份变化
            const handleMonthChange = (value, type = 'deadline') => {
                // 根据类型设置值
                if (type === 'create') {
                    selectedCreateMonth.value = value;
                } else { // deadline
                    selectedMonth.value = value;
                }
                
                // 调用通用筛选函数
                applyFilters();
            };

            // 删除项目
            const deleteProject = () => {
                const { id, name } = projectDialog.value.project;
                
                // 确保确认名称匹配
                if (projectDialog.value.deleteConfirmName !== name) {
                    return;
                }
                
                // 最终确认对话框
                ElementPlus.ElMessageBox.confirm(
                    `您确定要删除项目 "${name}" 吗？此操作不可逆！`,
                    '删除确认',
                    {
                        confirmButtonText: '确认删除',
                        cancelButtonText: '取消',
                        type: 'danger',
                        draggable: true
                    }
                ).then(() => {
                    // 显示加载状态
                    const loadingInstance = ElementPlus.ElLoading.service({
                        target: '.delete-project-confirm',
                        text: '正在删除项目...'
                    });
                    
                    // 调用实际的API
                    fetch('/projectmanagement/api/delete_project', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            project_id: id
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '删除项目失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // 更新本地数据
                            // 从项目列表中移除该项目
                            const index = projects.value.findIndex(p => p.id === id);
                            if (index !== -1) {
                                projects.value.splice(index, 1);
                            }
                            
                            // 关闭对话框
                            projectDialog.value.visible = false;
                            
                            // 如果当前选中的是被删除的项目，重置当前项目
                            if (currentProject.value && currentProject.value.id === id) {
                                currentProject.value = projects.value.length > 0 ? projects.value[0] : {};
                                // 刷新视图
                                if (projects.value.length > 0) {
                                    selectProject(currentProject.value);
                                } else {
                                    tasks.value = [];
                                    currentView.value = 'stats';
                                }
                            }
                            
                            // 显示成功消息
                            ElementPlus.ElMessage({
                                message: data.message || `项目 "${name}" 已成功删除`,
                                type: 'success'
                            });
                        } else {
                            throw new Error(data.message || '删除项目失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除项目失败:', error);
                        ElementPlus.ElMessage({
                            message: error.message,
                            type: 'error'
                        });
                    })
                    .finally(() => {
                        // 关闭加载状态
                        loadingInstance.close();
                    });
                }).catch(() => {
                    // 用户取消操作
                    ElementPlus.ElMessage({
                        message: '已取消删除操作',
                        type: 'info'
                    });
                });
            };

            // 切换视图
            const changeView = (view) => {
                if (view === currentView.value) return;
                
                // 先清除可能存在的图表实例，防止冲突
                if (window.projectChartInstances) {
                    Object.values(window.projectChartInstances).forEach(chart => {
                        if (chart && !chart.isDisposed()) {
                            chart.dispose();
                        }
                    });
                }
                window.projectChartInstances = {};
                
                // 处理从统计视图切换到其他视图的情况
                if (currentView.value === 'stats' && view !== 'stats') {
                    // 离开统计视图时，如果不是切换到项目视图，需要清除当前项目
                    if (view !== 'project') {
                        // 只清除id，保留其他信息
                        if (currentProject.value) {
                            currentProject.value = { ...currentProject.value, id: null };
                        }
                    }
                }
                // 当点击菜单项时，如果是导航菜单项(非project视图)，取消项目选中状态
                else if (view !== 'project') {
                    // 保存当前项目信息，但将id置为特殊值，表示没有项目被选中
                    // 这样不会完全丢失项目信息，但在UI中会显示为未选中
                    if (currentProject.value && currentProject.value.id) {
                        const savedProject = { ...currentProject.value };
                        savedProject.id = null; // 设置为null表示没有选中项目
                        currentProject.value = savedProject;
                    }
                }
                
                // 更新当前视图状态
                currentView.value = view;
                
                // 更新URL，不刷新页面
                const url = new URL(window.location.href);
                url.searchParams.set('view_type', view);
                if (view !== 'search' && view !== 'stats' && view !== 'approval' && view !== 'received' && currentProject.value && currentProject.value.id) {
                    url.searchParams.set('project_id', currentProject.value.id);
                } else if (view === 'search' || view === 'approval' || view === 'received') {
                    url.searchParams.delete('project_id');
                }
                window.history.pushState({}, document.title, url);
                
                // 处理首页导航
                if (view === 'home') {
                    // 加载首页数据
                    loadHomeData();
                    
                    // 采用多重保障机制确保图表正确渲染
                    // 1. 首次尝试渲染
                    setTimeout(() => {
                        renderTodoTimeDistribution();
                        renderTodoDailyTracking();
                    }, 100);
                    
                    // 2. 再次尝试渲染（以防首次渲染时DOM还未完全准备好）
                    setTimeout(() => {
                        renderTodoTimeDistribution();
                        renderTodoDailyTracking();
                    }, 300);
                    
                    // 3. 最终保障渲染（确保在DOM完全准备好后渲染）
                    setTimeout(() => {
                        renderTodoTimeDistribution();
                        renderTodoDailyTracking();
                    }, 600);
                    
                    return;
                }
                
                // 处理抄送给我的任务视图
                if (view === 'received') {
                    // 加载抄送给我的任务数据
                    tableLoading.value = true;
                    
                    // 重置当前页
                    currentPage.value = 1;
                    receivedTaskType.value = ''; // 空字符串表示未选择任何特定类型
                    // 构建API URL
                    const url = `/projectmanagement/api/cc_tasks?page=${currentPage.value}&page_size=${pageSize.value}`;
                    
                    fetch(url)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                tasks.value = data.tasks || [];
                                
                                // 更新总任务数量 - 后端分页
                                if (data.pagination && typeof data.pagination.total !== 'undefined') {
                                    totalTasks.value = data.pagination.total;
                                } else {
                                    // 兼容旧接口，如果没有返回分页信息，使用任务数组长度或total_count
                                    totalTasks.value = data.total_count || tasks.value.length;
                                }
                            } else {
                                ElementPlus.ElMessage.error(data.message || '加载抄送任务失败');
                                tasks.value = [];
                                totalTasks.value = 0;
                            }
                            tableLoading.value = false;
                        })
                        .catch(error => {
                            console.error('加载抄送任务失败:', error);
                            ElementPlus.ElMessage.error('加载抄送任务失败，请稍后重试');
                            tasks.value = [];
                            totalTasks.value = 0;
                            tableLoading.value = false;
                        });
                    
                    return;
                }
                
                // 处理任务统计视图
                if (view === 'task_stats') {
                    // 加载任务统计数据
                    loadProjectOptions(); // 加载项目列表
                    loadDepartments(); // 加载部门列表
                    loadAssigneeStats();
                    return;
                }
                
                // 处理项目统计视图
                if (view === 'stats') {
                    // 创建表示"全部项目"的特殊对象
                    const allProjectsObj = {
                        id: 'all',
                        name: '全部项目',
                        fromStatsPage: true
                    };
                    
                    // 更新当前项目
                    currentProject.value = allProjectsObj;
                    
                    // 确保其他项目没有被错误选中
                    projects.value.forEach(p => {
                        p.fromStatsPage = false;
                    });
                    
                    // 更新URL
                    const url = new URL(window.location.href);
                    url.searchParams.set('project_id', 'all');
                    window.history.pushState({}, document.title, url);
                    
                    // 将当前项目暴露给window对象，供图表使用
                    window.currentProject = currentProject.value;
                    
                    // 延迟渲染确保DOM元素已就绪
                    // 使用多层次保障渲染机制
                    setTimeout(() => {
                        renderProjectStatCharts();
                    }, 100);
                    
                    setTimeout(() => {
                        renderProjectStatCharts();
                    }, 300);
                    
                    setTimeout(() => {
                        renderProjectStatCharts();
                    }, 600);
                    
                    return;
                }
                
                // 处理审批视图
                if (view === 'approval') {
                    // 加载审批数据
                    tableLoading.value = true;
                    
                    setTimeout(() => {
                        taskDeleteApproval.loadPendingApprovals();
                        tableLoading.value = false;
                    }, 300);
                    
                    return;
                }
                
                // 处理模板管理视图
                if (view === 'templates') {
                    // Load templates when the view is activated
                    loadTemplates();
                    // Clear tasks if switching to templates view
                    tasks.value = []; 
                    templateTasks.value = []; // Clear template tasks too
                    currentTemplateDetails.value = null; // Clear template details
                    tableLoading.value = false;
                    return; // No need to fetch generic view data
                }
                
                // 处理模板详情视图 - 不需要特殊加载，由 viewTemplate 触发
                if (view === 'template_details') {
                    tasks.value = []; // Clear project tasks
                    tableLoading.value = false;
                    return; // Data loading is handled by loadTemplateTasks
                }
                
                // 如果是项目管理视图，不需要加载任务数据
                if (view === 'manage') {
                    tasks.value = []; // Clear tasks for manage/templates view
                    tableLoading.value = false;
                    return;
                }
                // 重置当前页
                currentPage.value = 1;
                // 其他视图加载数据
                loadViewData(view);
            };
            
            // 加载视图数据的通用方法
            const loadViewData = (view) => {
                // 显示加载状态
                tableLoading.value = true;
                tasks.value = [];

                // 使用AJAX获取数据
                let apiUrl = `/projectmanagement/api/view?view_type=${view}` + 
                    (view !== 'search' && currentProject.value.id ? `&project_id=${currentProject.value.id}` : '') +
                    (view === 'search' && searchQuery.value ? `&query=${encodeURIComponent(searchQuery.value)}` : '');
                
                // 添加分页参数
                apiUrl += `&page=${currentPage.value}&page_size=${pageSize.value}`;
                
                fetch(apiUrl)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取数据失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        tableLoading.value = false;
                        if (data.success) {
                            tasks.value = data.tasks || [];
                            // 如果是项目视图，可能需要更新当前项目
                            if (view === 'project' && data.current_project) {
                                currentProject.value = data.current_project;
                            }
                            
                            // 更新总任务数量 - 后端分页
                            if (data.pagination && typeof data.pagination.total !== 'undefined') {
                                totalTasks.value = data.pagination.total;
                            } else {
                                // 兼容旧接口，如果没有返回分页信息，使用任务数组长度或total_count
                                totalTasks.value = data.total_count || tasks.value.length;
                            }
                            
                            // 清除任务抽屉
                            if (drawer.value.visible) {
                                closeDrawer();
                            }
                        } else {
                            throw new Error(data.message || '获取数据失败');
                        }
                    })
                    .catch(error => {
                        tableLoading.value = false;
                        ElementPlus.ElMessage({
                            message: error.message,
                            type: 'error'
                        });
                    });
            };
            
            // 加载所有可管理项目的成员数量
            const loadProjectMemberCounts = () => {
                // 只处理可管理的项目
                const manageableProjects = projects.value.filter(project => {
                    // 超级管理员可以管理所有项目
                    if (userInfo.value.role === '超级管理员') {
                        return true;
                    }
                    
                    // 普通用户只能看到自己是管理员的项目
                    return project.user_role === '管理员' || project.user_role === 'admin';
                });
                
                // 如果没有可管理的项目，直接返回
                if (manageableProjects.length === 0) {
                    return;
                }
                
                // 为每个可管理的项目加载成员数量
                manageableProjects.forEach(project => {
                    fetch(`/projectmanagement/api/project_members?project_id=${project.id}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('获取项目成员失败');
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success && data.project) {
                                // 更新项目列表中的成员数量
                                const index = projects.value.findIndex(p => p.id === project.id);
                                if (index !== -1) {
                                    projects.value[index].member_count = data.project.member_count || 0;
                                }
                            }
                        })
                        .catch(error => {
                            console.error(`加载项目 ${project.id} 成员数量失败:`, error);
                        });
                });
            };

            // 加载首页数据
            const loadHomeData = () => {
                console.log('Loading home data with stats time range:', statsTimeRange.value, 'customDateRange:', customDateRange.value);
                homeLoading.value = true;
                
                // 获取"我要办"和"我发起"的任务列表
                Promise.all([
                    fetch('/projectmanagement/api/view?view_type=received'),
                    fetch('/projectmanagement/api/view?view_type=created')
                ])
                .then(responses => {
                    // 检查所有响应是否成功
                    for (const response of responses) {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取首页数据失败');
                            });
                        }
                    }
                    return Promise.all(responses.map(res => res.json()));
                })
                .then(([receivedData, createdData]) => {
                    console.log('Received data:', receivedData);
                    console.log('Created data:', createdData);
                    
                    if (receivedData.success && createdData.success) {
                        // 更新"我要办"的任务
                        myTodoTasks.value = receivedData.tasks || [];
                        console.log('Todo tasks:', myTodoTasks.value);
                        
                        // 更新"我发起"的任务
                        myCreatedTasks.value = createdData.tasks || [];
                        console.log('Created tasks:', myCreatedTasks.value);
                        
                        // 添加任务统计日志，比较两种任务数量
                        const todoTaskIds = new Set(myTodoTasks.value.map(task => task.id));
                        const createdTaskIds = new Set(myCreatedTasks.value.map(task => task.id));
                        const allTaskIds = new Set([...todoTaskIds, ...createdTaskIds]);
                        
                        console.log('任务统计汇总:', {
                            '我的任务数量': myTodoTasks.value.length,
                            '我的要求数量': myCreatedTasks.value.length,
                            '去重后总数': allTaskIds.size,
                            '两者都有的任务数': todoTaskIds.size + createdTaskIds.size - allTaskIds.size
                        });
                        
                        // 不再添加示例任务，直接显示空列表
                        if (myTodoTasks.value.length === 0) {
                            myTodoTasks.value = [];
                        }
                        
                        if (myCreatedTasks.value.length === 0) {
                            myCreatedTasks.value = [];
                        }
                    } else {
                        throw new Error('获取首页数据失败');
                    }
                })
                .catch(error => {
                    console.error('Error loading home data:', error);
                    
                    ElementPlus.ElMessage({
                        message: error.message,
                        type: 'error'
                    });
                })
                .finally(() => {
                    homeLoading.value = false;
                    
                    // 添加延迟确保DOM元素已渲染
                    setTimeout(() => {
                        // 渲染任务时间分布图表
                        renderTodoTimeDistribution();
                        
                        // 渲染任务每日追踪图表
                        renderTodoDailyTracking();
                    }, 100);
                });
            };

            // 添加时间范围筛选相关的状态
            const statsDateRange = ref('current'); // 默认显示本月
            const statsCustomDateRange = ref(null);

            // 禁用未来日期
            const disableFutureDates = (date) => {
                return date > new Date();
            };

            // 处理统计时间范围变化
            const handleStatsDateRangeChange = (value) => {
                if (value === 'custom') {
                    // 如果选择自定义，但还没有设置日期范围，默认设置为最近30天
                    if (!statsCustomDateRange.value) {
                        const end = new Date();
                        const start = new Date();
                        start.setDate(start.getDate() - 30);
                        statsCustomDateRange.value = [
                            start.toISOString().split('T')[0],
                            end.toISOString().split('T')[0]
                        ];
                    }
                } else {
                    // 根据选择的时间范围重新加载数据
                    loadStatsData(value);
                }
            };

            // 处理自定义日期范围变化
            const handleStatsCustomDateRangeChange = (value) => {
                if (value) {
                    loadStatsData('custom', value);
                }
            };

            // 加载统计数据
            const loadStatsData = (dateRange, customRange = null) => {
                if (!currentProject.value || !currentProject.value.id) return;

                tableLoading.value = true;
                let url = `/projectmanagement/api/project_tasks?project_id=${currentProject.value.id}&stats=true`;

                // 添加日期范围参数
                if (dateRange === 'current') {
                    const now = new Date();
                    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
                    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                    url += `&start_date=${firstDay.toISOString().split('T')[0]}&end_date=${lastDay.toISOString().split('T')[0]}`;
                } else if (dateRange === 'last') {
                    const now = new Date();
                    const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);
                    url += `&start_date=${firstDay.toISOString().split('T')[0]}&end_date=${lastDay.toISOString().split('T')[0]}`;
                } else if (dateRange === 'custom' && customRange) {
                    url += `&start_date=${customRange[0]}&end_date=${customRange[1]}`;
                }

                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('获取统计数据失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // 更新图表
                            renderProjectStatCharts();
                        } else {
                            throw new Error(data.message || '获取统计数据失败');
                        }
                    })
                    .catch(error => {
                        ElementPlus.ElMessage({
                            message: error.message,
                            type: 'error'
                        });
                    })
                    .finally(() => {
                        tableLoading.value = false;
                    });
            };

            // 从pm_task_delete_approval.js导入任务删除审批功能
            const taskDeleteApproval = initializeTaskDeleteApproval(currentProject,totalTasks, ElementPlus);
            // 将taskDeleteApproval添加到window对象，使其在其他模块中可用
            window.taskDeleteApproval = taskDeleteApproval;
            const pendingApprovalCount = taskDeleteApproval.pendingApprovalCount;
            const loadPendingApprovals = taskDeleteApproval.loadPendingApprovals;

            // 监听待审批任务数量变化，更新导航菜单角标
            watch(pendingApprovalCount, (newCount) => {
                // const approvalNavItem = navItems.value.find(item => item.value === 'approval');
                // if (approvalNavItem) {
                //     approvalNavItem.badge = newCount || 0;
                // }
                const homeNavItem = navItems.value.find(item => item.value === 'home');
                if (homeNavItem) {
                    homeNavItem.badge = newCount || 0;
                }
                homeApprovalBadgeCount.value = newCount || 0;
            });

            // 在组件挂载时加载待审批任务
            onMounted(async () => {
                await loadPendingApprovals();
            });

            // 组件挂载后
            onMounted(() => {
                // 将当前项目暴露给window对象，供图表使用
                window.currentProject = currentProject.value;
                
                // 从URL获取当前视图和项目参数
                const urlParams = new URLSearchParams(window.location.search);
                const viewType = urlParams.get('view_type');
                const projectId = urlParams.get('project_id');
                const taskId = urlParams.get('task_id');
                
                // 如果URL中指定了视图类型，更新当前视图
                if (viewType && viewType !== currentView.value) {
                    console.log(`从URL参数更新视图类型: ${viewType}`);
                    currentView.value = viewType;
                } else if (!viewType && window.location.pathname.includes('task_stats')) {
                    // 如果URL路径包含task_stats但没有view_type参数，则设置为任务统计视图
                    console.log('URL路径包含task_stats，自动设置为任务统计视图');
                    currentView.value = 'task_stats';
                }
                
                // 如果URL中有task_id参数，加载任务详情
                if (taskId) {
                    // 在调用handleRowClick之前，构造一个包含任务ID的模拟任务对象
                    fetch(`/projectmanagement/api/task/${taskId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.task) {
                                handleRowClick(data.task);
                            }
                        })
                        .catch(error => {
                            console.error('加载任务详情失败:', error);
                        });
                }
                
                // 如果有初始化的任务ID，加载任务详情
                if (initData.selectedTaskId) {
                    // 在调用handleRowClick之前，构造一个包含任务ID的模拟任务对象
                    fetch(`/projectmanagement/api/task/${initData.selectedTaskId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.task) {
                                handleRowClick(data.task);
                            }
                        })
                        .catch(error => {
                            console.error('加载任务详情失败:', error);
                        });
                }
                
                // 如果当前视图是任务统计视图，自动加载任务统计数据
                if (currentView.value === 'task_stats') {
                    console.log('当前是任务统计视图，自动加载统计数据');
                    loadProjectOptions(); // 加载项目列表
                    loadDepartments(); // 加载部门列表
                    loadAssigneeStats();
                }
                
                // 根据当前视图执行相应的初始化操作
                if (currentView.value === 'project' && 
                    currentProject.value && 
                    currentProject.value.id && 
                    tasks.value.length === 0) {
                    
                    console.log('初始加载项目任务:', currentProject.value.name);
                    selectProject(currentProject.value);
                }
                else if (currentView.value === 'home') {
                    loadHomeData();
                }
                else if (currentView.value === 'stats') {
                    // 创建表示"全部项目"的特殊对象
                    const allProjectsObj = {
                        id: 'all',
                        name: '全部项目',
                        fromStatsPage: true
                    };
                    
                    // 更新当前项目
                    currentProject.value = allProjectsObj;
                    
                    // 确保其他项目没有被错误选中
                    projects.value.forEach(p => {
                        p.fromStatsPage = false;
                    });
                    
                    // 更新URL
                    const url = new URL(window.location.href);
                    url.searchParams.set('project_id', 'all');
                    window.history.pushState({}, document.title, url);
                    
                    // 将当前项目暴露给window对象，供图表使用
                    window.currentProject = currentProject.value;
                    
                    setTimeout(() => {
                        renderProjectStatCharts();
                    }, 300);
                    
                    // 初始化时间范围
                    loadStatsData('current');
                }
                else if (currentView.value === 'approval') {
                    // 页面加载或刷新后，如果当前视图是审批视图，加载审核数据
                    console.log('初始化审批视图，加载审批数据');
                    // 延迟执行确保组件和引用都已准备好
                    setTimeout(() => {
                        taskDeleteApproval.loadPendingApprovals();
                    }, 300);
                }
                else if (currentView.value === 'received') {
                    // 页面加载或刷新后，如果当前视图是抄送给我的任务视图，加载抄送任务数据
                    console.log('初始化抄送给我的任务视图，加载抄送任务数据');
                    // 延迟执行确保组件和引用都已准备好
                    setTimeout(() => {
                        // 重置当前页
                        currentPage.value = 1;
                        
                        // 构建API URL
                        const url = `/projectmanagement/api/cc_tasks?page=${currentPage.value}&page_size=${pageSize.value}`;
                        
                        fetch(url)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    tasks.value = data.tasks || [];
                                    
                                    // 更新总任务数量 - 后端分页
                                    if (data.pagination && typeof data.pagination.total !== 'undefined') {
                                        totalTasks.value = data.pagination.total;
                                    } else {
                                        // 兼容旧接口，如果没有返回分页信息，使用任务数组长度或total_count
                                        totalTasks.value = data.total_count || tasks.value.length;
                                    }
                                } else {
                                    ElementPlus.ElMessage.error(data.message || '加载抄送任务失败');
                                    tasks.value = [];
                                    totalTasks.value = 0;
                                }
                                tableLoading.value = false;
                            })
                            .catch(error => {
                                console.error('加载抄送任务失败:', error);
                                ElementPlus.ElMessage.error('加载抄送任务失败，请稍后重试');
                                tasks.value = [];
                                totalTasks.value = 0;
                                tableLoading.value = false;
                            });
                    }, 300);
                }
                
                // 加载所有可管理项目的成员数量
                loadProjectMemberCounts();
                
                // 监听项目更新事件，更新项目列表
                document.addEventListener('project-updated', (event) => {
                    const updatedProject = event.detail;
                    
                    // 更新项目列表中的项目名称和描述
                    const index = projects.value.findIndex(p => p.id === updatedProject.id);
                    if (index !== -1) {
                        projects.value[index].name = updatedProject.name;
                        projects.value[index].description = updatedProject.description;
                        
                        // 如果更新的是当前选中的项目，同步更新currentProject
                        if (currentProject.value && currentProject.value.id === updatedProject.id) {
                            currentProject.value.name = updatedProject.name;
                            currentProject.value.description = updatedProject.description;
                            
                            // 如果当前是项目视图，更新页面标题
                            if (currentView.value === 'project') {
                                pageTitle.value = `项目：${updatedProject.name}`;
                            }
                        }
                    }
                    
                    console.log('项目已更新:', updatedProject.name);
                });

                // 添加事件监听器，处理新项目创建
                document.addEventListener('project-created', (event) => {
                    console.log('接收到project-created事件', event.detail);
                    const newProject = event.detail.project;
                    
                    // 添加新项目到项目列表
                    if (newProject && newProject.id) {
                        // 确保项目不重复
                        const exists = projects.value.some(p => p.id === newProject.id);
                        if (!exists) {
                            console.log('添加新项目到列表:', newProject);
                            projects.value.push({
                                ...newProject,
                                member_count: 1  // 创建者作为唯一成员
                            });
                        }
                    }
                });
                
                // 添加任务删除的事件监听器
                document.addEventListener('task-deleted', (event) => {
                    console.log('接收到task-deleted事件', event.detail);
                    const taskId = event.detail.taskId;
                    
                    // 从所有可能的任务列表中移除该任务
                    if (tasks.value.length > 0) {
                        const index = tasks.value.findIndex(t => t.id === taskId);
                        if (index !== -1) {
                            console.log('从任务列表中删除任务:', tasks.value[index].name);
                            tasks.value.splice(index, 1);
                        }
                    }
                    
                    // 从首页任务列表中移除
                    if (myTodoTasks.value.length > 0) {
                        const todoIndex = myTodoTasks.value.findIndex(t => t.id === taskId);
                        if (todoIndex !== -1) {
                            console.log('从我的任务列表中删除任务:', myTodoTasks.value[todoIndex].name);
                            myTodoTasks.value.splice(todoIndex, 1);
                        }
                    }
                    
                    if (myCreatedTasks.value.length > 0) {
                        const createdIndex = myCreatedTasks.value.findIndex(t => t.id === taskId);
                        if (createdIndex !== -1) {
                            console.log('从我的要求列表中删除任务:', myCreatedTasks.value[createdIndex].name);
                            myCreatedTasks.value.splice(createdIndex, 1);
                        }
                    }
                    
                    // 如果抽屉打开且显示的是被删除的任务，关闭抽屉
                    if (drawer.value.visible && drawer.value.taskId === taskId) {
                        console.log('关闭任务抽屉，因为当前任务已被删除');
                        closeDrawer();
                    }
                });
                
                // 处理导航到管理视图事件
                document.addEventListener('navigate-to-manage', () => {
                    console.log('导航到项目管理视图');
                    currentView.value = 'manage';
                });
                
                // 监听重置负责人全选状态事件
                document.addEventListener('reset-select-all-assignees', () => {
                    console.log('重置负责人全选状态');
                    selectAllAssignees.value = false;
                });
            });

            // 监听日期范围变化，重新渲染图表
            watch([statsTimeRange, customDateRange], () => {
                if (currentView.value === 'home') {
                    // 添加延迟确保DOM元素已渲染
                    setTimeout(() => {
                        renderTodoTimeDistribution();
                        renderTodoDailyTracking();
                    }, 100);
                }
            });

            // 监听视图变化
            watch(currentView, (newView) => {
                if (newView === 'stats') {
                    // 当切换到统计视图时，渲染项目统计图表
                    setTimeout(() => {
                        renderProjectStatCharts();
                    }, 300);
                } else if (newView === 'templates') {
                    // When switching to the template view, load the templates
                    loadTemplates();
                    templateTasks.value = []; // Clear template tasks if switching FROM details view
                    currentTemplateDetails.value = null;
                } else if (newView === 'template_details') {
                    // Usually triggered by viewTemplate, but handle direct URL access or refresh if needed?
                    // For now, assume loadTemplateTasks is called correctly by viewTemplate
                } else if (newView === 'approval') {
                    // 当切换到审批视图时，加载待处理的审核请求
                    taskDeleteApproval.loadPendingApprovals();
                } else if (newView === 'received') {
                    // 当切换到抄送给我的任务视图时，加载抄送任务数据
                    console.log('切换到抄送给我的任务视图，加载抄送任务数据');
                    // 这里不需要额外处理，因为changeView函数已经处理了加载逻辑
                } else if (newView === 'task_stats') {
                    // 当切换到任务统计视图时，加载任务统计数据
                    console.log('切换到任务统计视图，加载统计数据');
                    // loadProjectOptions(); // 加载项目列表
                    // loadAssigneeStats(); // 加载任务统计数据
                } else {
                    // Clear template tasks when switching to other views
                    templateTasks.value = [];
                    currentTemplateDetails.value = null;
                }
            });

            // 监听当前项目变化
            watch(currentProject, () => {
                if (currentView.value === 'stats') {
                    // 当项目变化时，刷新项目统计图表
                    setTimeout(() => {
                        // 将当前项目暴露给window对象，供图表使用
                        window.currentProject = currentProject.value;
                        renderProjectStatCharts();
                    }, 300);
                }
                
                // 始终将当前项目暴露给window对象
                window.currentProject = currentProject.value;
            });

            // 处理项目选择变化
            const handleProjectChange = (projectId) => {
                if (!projectId) return;
                
                // 处理"全部项目"选项
                if (projectId === 'all') {
                    // 创建表示"全部项目"的特殊对象
                    const allProjectsObj = {
                        id: 'all',
                        name: '全部项目',
                        fromStatsPage: true
                    };
                    
                    // 更新当前项目
                    currentProject.value = allProjectsObj;
                    
                    // 确保其他项目没有被错误选中
                    projects.value.forEach(p => {
                        p.fromStatsPage = false;
                    });
                    
                    // 更新URL，不刷新页面
                    const url = new URL(window.location.href);
                    url.searchParams.set('project_id', 'all');
                    window.history.pushState({}, document.title, url);
                    
                    // 将当前项目暴露给window对象，供图表使用
                    window.currentProject = currentProject.value;
                    
                    // 显示加载状态
                    tableLoading.value = true;
                    
                    // 渲染项目统计图表
                    setTimeout(() => {
                        renderProjectStatCharts();
                        tableLoading.value = false;
                    }, 300);
                    
                    return;
                }
                
                // 从项目列表中查找选中的项目
                const selectedProject = projects.value.find(p => p.id === projectId);
                if (!selectedProject) return;
                
                // 更新当前项目
                // 添加标记表示这是从统计页面选择的项目，防止侧边栏出现多选
                selectedProject.fromStatsPage = true; 
                currentProject.value = selectedProject;
                
                // 确保其他项目没有被错误选中
                projects.value.forEach(p => {
                    if (p.id !== projectId) {
                        p.fromStatsPage = false;
                    }
                });
                
                // 更新URL，不刷新页面
                const url = new URL(window.location.href);
                url.searchParams.set('project_id', projectId);
                window.history.pushState({}, document.title, url);
                
                // 将当前项目暴露给window对象，供图表使用
                window.currentProject = currentProject.value;
                
                // 显示加载状态
                tableLoading.value = true;
                
                // 渲染项目统计图表
                setTimeout(() => {
                    renderProjectStatCharts();
                    tableLoading.value = false;
                }, 300);
            };

            // 添加当前模板任务页码相关状态
            const currentTemplateTasksPage = ref(1);
            const templatePageSize = ref(14);
            const paginatedTemplateTasks = computed(() => {
                const startIndex = (currentTemplateTasksPage.value - 1) * templatePageSize.value;
                const endIndex = startIndex + templatePageSize.value;
                return templateTasks.value.slice(startIndex, endIndex);
            });
            const handleTemplateTasksPageChange = (newPage) => {
                currentTemplateTasksPage.value = newPage;
                // 如果有抽屉打开，则关闭它
                if (drawer.value.visible) {
                    closeDrawer();
                }
            };

            // 新增：返回模板列表函数
            const goBackToTemplates = () => {
                currentView.value = 'templates';
                // 清理模板详情数据（可选，取决于是否希望保留状态）
                // currentTemplateDetails.value = null;
                // templateTasks.value = []; 
            };

            // 导入模板对话框状态
            const importTemplateDialog = ref({
                visible: false,
                loading: false,
                templates: [],
                selectedTemplateId: null
            });

            // 获取选中模板的描述
            const getSelectedTemplateDescription = () => {
                const selectedTemplate = importTemplateDialog.value.templates.find(
                    t => t.id === importTemplateDialog.value.selectedTemplateId
                );
                return selectedTemplate ? (selectedTemplate.description || '该模板暂无描述') : '';
            };

            // 显示导入模板对话框
            const showImportTemplateDialog = async () => {
                if (!currentProject.value || !currentProject.value.id) {
                    ElementPlus.ElMessage.warning('请先选择一个项目');
                    return;
                }

                importTemplateDialog.value.visible = true;
                importTemplateDialog.value.loading = true;
                importTemplateDialog.value.selectedTemplateId = null;

                try {
                    const response = await fetch('/projectmanagement/api/templates');
                    const result = await response.json();

                    if (result.success) {
                        importTemplateDialog.value.templates = result.templates || [];
                    } else {
                        throw new Error(result.message || '获取模板列表失败');
                    }
                } catch (error) {
                    console.error('获取模板列表失败:', error);
                    ElementPlus.ElMessage.error(error.message || '获取模板列表失败');
                } finally {
                    importTemplateDialog.value.loading = false;
                }
            };

            // 关闭导入模板对话框
            const closeImportTemplateDialog = () => {
                importTemplateDialog.value.visible = false;
                // 使用nextTick等待对话框完全关闭后再清除数据
                Vue.nextTick(() => {
                    importTemplateDialog.value.templates = [];
                    importTemplateDialog.value.selectedTemplateId = null;
                });
            };

            // 执行导入模板
            const importTemplate = async () => {
                const templateId = importTemplateDialog.value.selectedTemplateId;
                const projectId = currentProject.value.id;

                if (!templateId || !projectId) {
                    ElementPlus.ElMessage.warning('请选择要导入的模板');
                    return;
                }

                importTemplateDialog.value.loading = true;

                try {
                    const response = await fetch(`/projectmanagement/api/template/import/${templateId}/to_project/${projectId}`, {
                        method: 'POST'
                    });

                    const result = await response.json();

                    if (result.success) {
                        ElementPlus.ElMessage.success(`成功导入 ${result.task_count} 个任务`);
                        closeImportTemplateDialog();
                        
                        // 刷新任务列表
                        if (currentView.value === 'project') {
                            selectProject(currentProject.value);
                        }
                    } else {
                        throw new Error(result.message || '导入模板失败');
                    }
                } catch (error) {
                    console.error('导入模板失败:', error);
                    ElementPlus.ElMessage.error(error.message || '导入模板失败');
                } finally {
                    importTemplateDialog.value.loading = false;
                }
            };

            // 替换原有的deleteTask函数
            const deleteTask = (task) => {
                console.log('deleteTask被调用，传入的任务对象:', task);
                
                // 如果传入的是事件对象，使用drawer中的任务
                if (task instanceof Event || !task) {
                    console.log('使用drawer中的任务:', drawer.value.task);
                    task = drawer.value.task;
                }
                
                // 确保任务对象包含必要的信息
                if (task && typeof task === 'object' && task.id && task.project_id) {
                    return taskDeleteApproval.deleteTaskWithApproval(task);
                } else {
                    console.error('无效的任务对象:', {
                        task: task,
                        taskType: typeof task,
                        hasId: task?.id,
                        hasProjectId: task?.project_id,
                        drawerId: drawer.value?.taskId,
                        drawerTask: drawer.value?.task
                    });
                    ElementPlus.ElMessage.error('无法获取完整的任务信息');
                }
            };

            const homeApprovalBadgeCount = ref(0);

            // 任务统计相关数据
            const assigneeStats = ref([]);
            const assigneeStatsPageSize = ref(20);
            const assigneeStatsCurrentPage = ref(1);
            const assigneeStatsTotal = ref(0);
            const assigneeNameFilter = ref('');
            const assigneeDateFilter = ref('all');
            const assigneeCustomDateRange = ref(null);
            const assigneeProjectFilter = ref(''); // 新增：项目筛选
            const assigneeUrgencyFilter = ref(''); // 新增：紧急程度筛选
            const assigneeDeadlineFilter = ref(''); // 新增：截至日期筛选
            const assigneeCustomDeadlineRange = ref(null); // 新增：自定义截至日期范围
            const assigneeDepartmentFilter = ref(''); // 新增：部门筛选
            const departments = ref([]); // 新增：部门列表
            const projectOptions = ref([]); // 新增：项目选项列表
            const exportLoading = ref(false); // 新增：导出加载状态

            // 新增：加载部门列表
            const loadDepartments = () => {
                fetch('/projectmanagement/api/departments')
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取部门列表失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            departments.value = data.departments || [];
                        } else {
                            throw new Error(data.message || '获取部门列表失败');
                        }
                    })
                    .catch(error => {
                        console.error('加载部门列表失败:', error);
                        ElementPlus.ElMessage.error(`加载部门列表失败: ${error.message}`);
                    });
            };

            // 任务详情对话框相关数据
            const taskDetailDialog = ref({
                visible: false,
                title: '',
                loading: false,
                exportLoading: false, // 添加导出按钮的加载状态
                taskType: '',
                userId: null,
                userName: '',
                tasks: [],
                pagination: {
                    currentPage: 1,
                    pageSize: 10,
                    total: 0
                }
            });

            // 日期筛选选项
            const dateFilterOptions = [
                { value: 'all', label: '全部' },
                { value: 'current_month', label: '本月创建' },
                { value: 'last_month', label: '上月创建' },
                { value: 'current_year', label: '本年创建' },
                { value: 'custom', label: '自定义' }
            ];
            
            // 截至日期筛选选项
            const deadlineFilterOptions = [
                { value: 'all', label: '全部' },
                { value: 'current_month_deadline', label: '本月截至' },
                { value: 'last_month_deadline', label: '上月截至' },
                { value: 'current_year_deadline', label: '本年截至' },
                { value: 'custom_deadline', label: '自定义' }
            ];

            // 显示任务详情对话框
            const showTaskDetailDialog = (row, taskType) => {
                // 设置对话框基本信息
                taskDetailDialog.value.visible = true;
                taskDetailDialog.value.loading = true;
                taskDetailDialog.value.taskType = taskType;
                taskDetailDialog.value.userId = row.user_id;
                taskDetailDialog.value.userName = row.user_name;
                taskDetailDialog.value.tasks = [];
                taskDetailDialog.value.pagination.currentPage = 1;
                taskDetailDialog.value.pagination.total = 0;
                
                // 设置对话框标题
                const taskTypeLabels = {
                    'total_tasks': '全部任务',
                    'in_progress_tasks': '进行中任务',
                    'overdue_completed_tasks': '逾期完成任务',
                    'overdue_incomplete_tasks': '逾期未完成任务',
                    'overdue_tasks': '逾期任务',
                    'on_time_completed_tasks': '按时完成任务'
                };
                taskDetailDialog.value.title = `${row.user_name}的${taskTypeLabels[taskType] || '任务'}`;
                
                // 加载任务详情数据
                loadTaskDetailData();
            };
            
            // 关闭任务详情对话框
            const closeTaskDetailDialog = () => {
                taskDetailDialog.value.visible = false;
                taskDetailDialog.value.tasks = [];
            };
            
            // 处理任务详情分页变化
            const handleTaskDetailPageChange = (newPage) => {
                taskDetailDialog.value.pagination.currentPage = newPage;
                loadTaskDetailData();
            };
            
            // 加载任务详情数据
            const loadTaskDetailData = () => {
                const { userId, taskType, pagination } = taskDetailDialog.value;
                if (!userId || !taskType) return;
                
                taskDetailDialog.value.loading = true;
                
                // 构建请求URL
                let url = `/projectmanagement/api/user_task_details?user_id=${userId}&task_type=${taskType}&page=${pagination.currentPage}&page_size=${pagination.pageSize}`;
                
                // 添加日期筛选
                let actualDateFilter = assigneeDateFilter.value;
                if (actualDateFilter === 'custom' && assigneeCustomDateRange.value) {
                    url += `&date=${actualDateFilter}&start_date=${assigneeCustomDateRange.value[0]}&end_date=${assigneeCustomDateRange.value[1]}`;
                } else if (actualDateFilter !== 'all') {
                    url += `&date=${actualDateFilter}`;
                }
                
                // 添加项目筛选
                if (assigneeProjectFilter.value) {
                    url += `&project_id=${assigneeProjectFilter.value}`;
                }
                
                // 添加紧急程度筛选
                if (assigneeUrgencyFilter.value && assigneeUrgencyFilter.value !== 'all') {
                    url += `&urgency=${encodeURIComponent(assigneeUrgencyFilter.value)}`;
                }
                
                // 添加截至日期筛选
                if (assigneeDeadlineFilter.value && assigneeDeadlineFilter.value !== 'all') {
                    url += `&deadline_filter=${assigneeDeadlineFilter.value}`;
                    
                    // 如果是自定义截至日期范围
                    if (assigneeDeadlineFilter.value === 'custom_deadline' && assigneeCustomDeadlineRange.value) {
                        if (Array.isArray(assigneeCustomDeadlineRange.value) && 
                            assigneeCustomDeadlineRange.value.length === 2 &&
                            assigneeCustomDeadlineRange.value[0] && 
                            assigneeCustomDeadlineRange.value[1]) {
                            url += `&deadline_start_date=${assigneeCustomDeadlineRange.value[0]}&deadline_end_date=${assigneeCustomDeadlineRange.value[1]}`;
                        }
                    }
                }
                
                // 添加部门筛选
                if (assigneeDepartmentFilter.value) {
                    url += `&department=${encodeURIComponent(assigneeDepartmentFilter.value)}`;
                }
                
                // 添加日期筛选
                // if (actualDateFilter !== 'all') {
                //     url += `&date=${actualDateFilter}`;
                //     if (actualDateFilter === 'custom' && assigneeCustomDateRange.value) {
                //         url += `&start_date=${assigneeCustomDateRange.value[0]}&end_date=${assigneeCustomDateRange.value[1]}`;
                //     }
                // }
                
                // 发送请求
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取任务详情失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            taskDetailDialog.value.tasks = data.tasks || [];
                            taskDetailDialog.value.pagination.total = data.pagination?.total || 0;
                            
                            if (taskDetailDialog.value.tasks.length === 0) {
                                ElementPlus.ElMessage.info('未找到任务数据');
                            }
                        } else {
                            throw new Error(data.message || '获取任务详情失败');
                        }
                    })
                    .catch(error => {
                        console.error('加载任务详情数据失败:', error);
                        ElementPlus.ElMessage.error(`加载失败: ${error.message}`);
                    })
                    .finally(() => {
                        taskDetailDialog.value.loading = false;
                    });
            };

            // 新增：加载项目选项列表
            const loadProjectOptions = () => {
                fetch('/projectmanagement/api/projects')
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取项目列表失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            projectOptions.value = data.projects || [];
                        } else {
                            throw new Error(data.message || '获取项目列表失败');
                        }
                    })
                    .catch(error => {
                        console.error('加载项目列表失败:', error);
                        ElementPlus.ElMessage.error(`加载项目列表失败: ${error.message}`);
                    });
            };

            // 加载任务统计数据
            const loadAssigneeStats = () => {
                console.log('执行loadAssigneeStats函数，加载任务统计数据');
                // 显示加载状态
                tableLoading.value = true;
                assigneeStats.value = []; // 清空现有数据，防止旧数据显示
                
                // 验证自定义日期范围
                let actualDateFilter = assigneeDateFilter.value;
                if (actualDateFilter === 'custom') {
                    // 检查日期范围是否完整
                    if (!assigneeCustomDateRange.value || 
                        !Array.isArray(assigneeCustomDateRange.value) || 
                        assigneeCustomDateRange.value.length !== 2 ||
                        !assigneeCustomDateRange.value[0] || 
                        !assigneeCustomDateRange.value[1]) {
                        console.log('自定义日期范围不完整，回退到默认过滤器（本月）', assigneeCustomDateRange.value);
                        // 回退到默认过滤器
                        actualDateFilter = 'current_month';
                        // 可选：更新UI状态，使界面与实际使用的过滤器一致
                        assigneeDateFilter.value = 'current_month';
                        assigneeCustomDateRange.value = null;
                    }
                }
                
                let url = `/projectmanagement/api/assignee_task_stats?page=${assigneeStatsCurrentPage.value}&page_size=${assigneeStatsPageSize.value}`;
                
                // 添加姓名筛选
                if (assigneeNameFilter.value) {
                    url += `&name=${encodeURIComponent(assigneeNameFilter.value)}`;
                }
                
                // 添加项目筛选
                if (assigneeProjectFilter.value) {
                    url += `&project_id=${assigneeProjectFilter.value}`;
                }
                
                // 添加紧急程度筛选
                if (assigneeUrgencyFilter.value && assigneeUrgencyFilter.value !== 'all') {
                    url += `&urgency=${encodeURIComponent(assigneeUrgencyFilter.value)}`;
                }
                
                // 添加部门筛选
                if (assigneeDepartmentFilter.value) {
                    url += `&department=${encodeURIComponent(assigneeDepartmentFilter.value)}`;
                }
                
                // 添加日期筛选
                if (actualDateFilter !== 'all') {
                    url += `&date=${actualDateFilter}`;
                    if (actualDateFilter === 'custom' && assigneeCustomDateRange.value) {
                        url += `&start_date=${assigneeCustomDateRange.value[0]}&end_date=${assigneeCustomDateRange.value[1]}`;
                    }
                }
                
                // 添加截至日期筛选
                if (assigneeDeadlineFilter.value && assigneeDeadlineFilter.value !== 'all') {
                    url += `&deadline_filter=${assigneeDeadlineFilter.value}`;
                    
                    // 如果是自定义截至日期范围
                    if (assigneeDeadlineFilter.value === 'custom_deadline' && assigneeCustomDeadlineRange.value) {
                        if (Array.isArray(assigneeCustomDeadlineRange.value) && 
                            assigneeCustomDeadlineRange.value.length === 2 &&
                            assigneeCustomDeadlineRange.value[0] && 
                            assigneeCustomDeadlineRange.value[1]) {
                            url += `&deadline_start_date=${assigneeCustomDeadlineRange.value[0]}&deadline_end_date=${assigneeCustomDeadlineRange.value[1]}`;
                        }
                    }
                }

                console.log('请求URL:', url);
                
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取任务统计数据失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('获取到任务统计数据:', data);
                        if (data.success) {
                            assigneeStats.value = data.stats || [];
                            assigneeStatsTotal.value = data.pagination.total || 0;
                            
                            // 如果返回的数据为空，显示提示
                            if (assigneeStats.value.length === 0) {
                                ElementPlus.ElMessage.info('未找到符合条件的任务统计数据');
                            }
                        } else {
                            throw new Error(data.message || '获取任务统计数据失败');
                        }
                    })
                    .catch(error => {
                        console.error('加载任务统计数据出错:', error);
                        ElementPlus.ElMessage.error(`加载失败: ${error.message}`);
                        assigneeStats.value = []; // 确保错误时清空数据
                        assigneeStatsTotal.value = 0;
                    })
                    .finally(() => {
                        tableLoading.value = false;
                    });
            };

            // 处理页码变化
            const handleAssigneeStatsPageChange = (newPage) => {
                assigneeStatsCurrentPage.value = newPage;
                loadAssigneeStats();
            };

            // 处理每页显示数量变化
            const handleAssigneeStatsPageSizeChange = (newSize) => {
                assigneeStatsPageSize.value = newSize;
                assigneeStatsCurrentPage.value = 1;
                loadAssigneeStats();
            };

            // 统一的任务统计搜索处理函数
            const handleAssigneeStatsSearch = () => {
                assigneeStatsCurrentPage.value = 1;
                loadAssigneeStats();
            };

            // 处理姓名筛选回车事件
            const handleAssigneeNameFilterChange = () => {
                handleAssigneeStatsSearch();
            };

            // 导出任务统计数据为Excel
            const exportAssigneeStats = () => {
                exportLoading.value = true;
                
                // 构建导出URL
                let url = '/projectmanagement/api/export_assignee_stats';
                const params = new URLSearchParams();
                
                // 添加姓名筛选
                if (assigneeNameFilter.value) {
                    params.append('name', assigneeNameFilter.value);
                }
                
                // 添加项目筛选
                if (assigneeProjectFilter.value) {
                    params.append('project_id', assigneeProjectFilter.value);
                }
                
                // 添加紧急程度筛选
                if (assigneeUrgencyFilter.value && assigneeUrgencyFilter.value !== 'all') {
                    params.append('urgency', assigneeUrgencyFilter.value);
                }
                
                // 添加部门筛选
                if (assigneeDepartmentFilter.value) {
                    params.append('department', assigneeDepartmentFilter.value);
                }
                
                // 添加日期筛选
                if (assigneeDateFilter.value !== 'all') {
                    params.append('date', assigneeDateFilter.value);
                    if (assigneeDateFilter.value === 'custom' && assigneeCustomDateRange.value) {
                        params.append('start_date', assigneeCustomDateRange.value[0]);
                        params.append('end_date', assigneeCustomDateRange.value[1]);
                    }
                }
                
                // 添加截至日期筛选
                if (assigneeDeadlineFilter.value && assigneeDeadlineFilter.value !== 'all') {
                    params.append('deadline_filter', assigneeDeadlineFilter.value);
                    
                    // 如果是自定义截至日期范围
                    if (assigneeDeadlineFilter.value === 'custom_deadline' && assigneeCustomDeadlineRange.value) {
                        if (Array.isArray(assigneeCustomDeadlineRange.value) && 
                            assigneeCustomDeadlineRange.value.length === 2 &&
                            assigneeCustomDeadlineRange.value[0] && 
                            assigneeCustomDeadlineRange.value[1]) {
                            params.append('deadline_start_date', assigneeCustomDeadlineRange.value[0]);
                            params.append('deadline_end_date', assigneeCustomDeadlineRange.value[1]);
                        }
                    }
                }
                
                // 将参数添加到URL
                url = `${url}?${params.toString()}`;
                
                // 使用fetch API发送请求
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            // 如果响应状态码不是200，尝试解析JSON错误信息
                            return response.json().then(errorData => {
                                throw new Error(errorData.message || '导出失败');
                            });
                        }
                        
                        // 检查Content-Type是否为Excel文件
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
                            // 是Excel文件，进行下载
                            return response.blob();
                        } else {
                            // 不是Excel文件，可能是JSON错误信息
                            return response.json().then(data => {
                                throw new Error(data.message || '导出失败');
                            });
                        }
                    })
                    .then(blob => {
                        // 创建URL对象
                        const url = window.URL.createObjectURL(blob);
                        
                        // 创建临时链接并触发下载
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `任务统计_${new Date().toISOString().slice(0, 10)}.xlsx`;
                        document.body.appendChild(link);
                        link.click();
                        
                        // 清理
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(link);
                        
                        // 显示成功消息
                        ElementPlus.ElMessage.success('导出成功');
                    })
                    .catch(error => {
                        // 处理错误情况
                        console.error('导出失败:', error);
                        
                        // 显示友好的错误消息
                        if (error.message.includes('没有符合条件的数据可导出')) {
                            ElementPlus.ElMessage.warning('没有符合条件的数据可导出');
                        } else {
                            ElementPlus.ElMessage.error(`导出失败: ${error.message}`);
                        }
                    })
                    .finally(() => {
                        // 无论成功还是失败，都关闭加载状态
                        exportLoading.value = false;
                    });
            };

            // 监听日期筛选变化
            watch(assigneeDateFilter, (newValue) => {
                if (newValue !== 'custom') {
                    assigneeCustomDateRange.value = null;
                    handleAssigneeStatsSearch();
                }
            });

            // 监听自定义日期范围变化
            watch(assigneeCustomDateRange, (newValue) => {
                if (assigneeDateFilter.value === 'custom' && newValue) {
                    handleAssigneeStatsSearch();
                }
            });
            
            // 监听项目筛选变化
            watch(assigneeProjectFilter, (newValue) => {
                handleAssigneeStatsSearch();
            });
            
            // 监听紧急程度筛选变化
            watch(assigneeUrgencyFilter, (newValue) => {
                handleAssigneeStatsSearch();
            });
            
            // 监听截至日期筛选变化
            watch(assigneeDeadlineFilter, (newValue) => {
                handleAssigneeStatsSearch();
            });
            
            // 监听自定义截至日期范围变化
            watch(assigneeCustomDeadlineRange, (newValue) => {
                if (assigneeDeadlineFilter.value === 'custom_deadline' && newValue) {
                    handleAssigneeStatsSearch();
                }
            });
            
            // 监听部门筛选变化
            watch(assigneeDepartmentFilter, (newValue) => {
                handleAssigneeStatsSearch();
            });

            // 原处理页面跳转函数已移除，现使用Element UI分页组件自带的跳转功能

            // 获取表格行的样式类名
            const rowClassName = (row) => {
                // 根据任务紧急程度设置行样式
                // 如果逾期未完成任务超过3个，使用高亮红色样式
                if (row.overdue_incomplete_tasks > 3) {
                    return 'row-high';
                }
                // 如果逾期未完成任务大于0且小于等于3，使用中等警告样式
                else if (row.overdue_incomplete_tasks > 0) {
                    return 'row-medium';
                }
                return '';
            };

            // 计算总的进行中任务数
            const getTotalActiveTasks = () => {
                if (!assigneeStats.value || assigneeStats.value.length === 0) return 0;
                return assigneeStats.value.reduce((sum, item) => sum + (parseInt(item.in_progress_tasks) || 0), 0);
            };

            // 计算总的逾期任务数（包括逾期完成和逾期未完成）
            const getTotalOverdueTasks = () => {
                if (!assigneeStats.value || assigneeStats.value.length === 0) return 0;
                return assigneeStats.value.reduce((sum, item) => {
                    return sum + 
                        (parseInt(item.overdue_incomplete_tasks) || 0);
                }, 0);
            };

            // 监听负责人选择变化，更新全选状态
            watch(() => taskDialog.value.form.assignees, (newVal) => {
                if (taskDialog.value.availableAssignees.length > 0) {
                    // 判断是否所有可用负责人都被选中
                    selectAllAssignees.value = newVal.length === taskDialog.value.availableAssignees.length;
                }
            });

            // 监听可用负责人列表变化，在列表加载完成后检查是否需要更新全选状态
            watch(() => taskDialog.value.availableAssignees, (newVal) => {
                if (newVal.length > 0 && taskDialog.value.form.assignees) {
                    // 判断是否所有可用负责人都被选中
                    selectAllAssignees.value = taskDialog.value.form.assignees.length === newVal.length;
                }
            });

            // 添加日志删除相关函数
            const isWithinDeleteTimeLimit = (log) => {
                if(log.type !== 'reply'){
                    return false;
                }
                // 检查日志是否包含创建时间（优先使用created_at，如果没有则使用time）
                const timeField = log.created_at || log.time;
                if (!timeField) return false;
                
                // 解析日志创建时间
                const logTime = new Date(timeField).getTime();
                const currentTime = Date.now();
                
                // 计算时间差（毫秒）
                const timeDiff = currentTime - logTime;
                
                // 检查是否在3分钟（180000毫秒）内
                return timeDiff <= 180000;
            };

            // 删除日志函数
            const deleteLog = (logId, logType) => {
                if (!logId) {
                    ElementPlus.ElMessage.error('日志ID无效');
                    return;
                }
                
                if (!logType) {
                    ElementPlus.ElMessage.error('日志类型无效');
                    return;
                }
                
                // 确认删除
                ElementPlus.ElMessageBox.confirm(
                    '确定要删除此日志吗？此操作不可恢复。',
                    '删除确认',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                ).then(() => {
                    // 发送删除请求到后端
                    fetch('/projectmanagement/api/delete_log', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            log_id: logId,
                            type: logType
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '删除日志失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // 从任务日志列表中移除已删除的日志
                            if (drawer.value.task && drawer.value.task.logs) {
                                const index = drawer.value.task.logs.findIndex(log => log.id === logId);
                                if (index !== -1) {
                                    drawer.value.task.logs.splice(index, 1);
                                }
                            }
                            
                            ElementPlus.ElMessage({
                                message: '日志已成功删除',
                                type: 'success'
                            });
                        } else {
                            throw new Error(data.message || '删除日志失败');
                        }
                    })
                    .catch(error => {
                        ElementPlus.ElMessage({
                            message: error.message,
                            type: 'error'
                        });
                    });
                }).catch(() => {
                    // 用户取消删除操作
                });
            };

            // 修改导出任务详情数据函数
            const exportTaskDetailData = () => {
                const { userId, taskType, userName, tasks } = taskDetailDialog.value;
                
                // 检查是否有任务数据
                if (!tasks || tasks.length === 0) {
                    ElementPlus.ElMessage.warning('没有数据可导出，请先查询数据');
                    return;
                }
                
                if (!userId || !taskType) {
                    ElementPlus.ElMessage.warning('无法获取任务数据，请重试');
                    return;
                }
                
                taskDetailDialog.value.exportLoading = true;
                
                // 构建导出URL
                let url = `/projectmanagement/api/export_user_task_details?user_id=${userId}&task_type=${taskType}`;
                
                // 添加日期筛选
                let actualDateFilter = assigneeDateFilter.value;
                if (actualDateFilter === 'custom' && assigneeCustomDateRange.value) {
                    url += `&date=${actualDateFilter}&start_date=${assigneeCustomDateRange.value[0]}&end_date=${assigneeCustomDateRange.value[1]}`;
                } else if (actualDateFilter !== 'all') {
                    url += `&date=${actualDateFilter}`;
                }
                
                // 添加项目筛选
                if (assigneeProjectFilter.value) {
                    url += `&project_id=${assigneeProjectFilter.value}`;
                }
                
                // 添加紧急程度筛选
                if (assigneeUrgencyFilter.value && assigneeUrgencyFilter.value !== 'all') {
                    url += `&urgency=${encodeURIComponent(assigneeUrgencyFilter.value)}`;
                }
                
                // 添加部门筛选
                if (assigneeDepartmentFilter.value) {
                    url += `&department=${assigneeDepartmentFilter.value}`;
                }
                
                // 添加截止日期筛选
                if (assigneeDeadlineFilter.value !== 'all') {
                    url += `&deadline_filter=${assigneeDeadlineFilter.value}`;
                    if (assigneeDeadlineFilter.value === 'custom_deadline' && assigneeCustomDeadlineRange.value) {
                        url += `&deadline_start_date=${assigneeCustomDeadlineRange.value[0]}&deadline_end_date=${assigneeCustomDeadlineRange.value[1]}`;
                    }
                }
                
                // 创建一个隐藏的a标签来触发下载
                const link = document.createElement('a');
                link.href = url;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 显示导出成功提示
                ElementPlus.ElMessage.success('任务数据导出成功');
                
                // 设置一个短暂的延迟后关闭加载状态
                setTimeout(() => {
                    taskDetailDialog.value.exportLoading = false;
                }, 1000);
            };

            // 导出项目任务为Excel
            const exportProjectTasks = () => {
                // 检查是否有任务数据
                if (!paginatedTasks.value || paginatedTasks.value.length === 0) {
                    ElementPlus.ElMessage.warning('没有数据可导出，请先查询数据');
                    return;
                }
                
                exportLoading.value = true;
                
                // 构建导出URL
                let url = '/api/project/tasks/export?';
                
                // 添加项目ID
                if (currentProject.value && currentProject.value.id) {
                    url += `project_id=${currentProject.value.id}&`;
                }
                
                // 添加任务状态筛选
                if (taskStatusFilter.value && taskStatusFilter.value !== 'all') {
                    url += `status=${taskStatusFilter.value}&`;
                }
                
                // 添加创建日期筛选
                if (selectedCreateMonth.value) {
                    url += `create_month=${selectedCreateMonth.value}&`;
                }
                
                // 添加搜索关键词
                if (projectTaskSearchQuery.value) {
                    url += `search=${encodeURIComponent(projectTaskSearchQuery.value)}&`;
                }
                
                // 移除最后一个&符号
                if (url.endsWith('&')) {
                    url = url.slice(0, -1);
                }
                
                // 创建一个隐藏的a标签来触发下载
                const link = document.createElement('a');
                link.href = url;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 显示导出成功提示
                ElementPlus.ElMessage.success('任务数据导出成功');
                
                // 设置一个短暂的延迟后关闭加载状态
                setTimeout(() => {
                    exportLoading.value = false;
                }, 1000);
            };

            // 统计任务对话框数据
            const statsTaskDialog = ref({
                visible: false,
                loading: false,
                title: '',
                tasks: [],
                currentPage: 1,
                pageSize: 10,
                total: 0,
                type: '',
                timeRange: ''
            });

            // 处理统计项点击
            const handleStatsItemClick = async (type) => {
                statsTaskDialog.value.visible = true;
                statsTaskDialog.value.loading = true;
                statsTaskDialog.value.type = type;
                statsTaskDialog.value.timeRange = statsTimeRange.value;
                statsTaskDialog.value.currentPage = 1;
                
                // 设置对话框标题
                const titles = {
                    unfinished: '未完成任务',
                    finished: '已完成任务',
                    total: '全部任务',
                    overdue: '已逾期任务'
                };
                statsTaskDialog.value.title = `${titles[type]} - ${getStatsTimeRangeLabel()}`;
                
                // 加载任务数据
                await loadStatsTaskData();
            };

            // 加载统计任务数据
            const loadStatsTaskData = async () => {
                try {
                    // 构建请求参数
                    const params = new URLSearchParams({
                        type: statsTaskDialog.value.type,
                        time_range: statsTaskDialog.value.timeRange,
                        page: statsTaskDialog.value.currentPage,
                        page_size: statsTaskDialog.value.pageSize
                    });

                    // 如果是自定义日期范围，添加日期参数
                    if (statsTaskDialog.value.timeRange === 'custom' && customDateRange.value) {
                        params.append('start_date', customDateRange.value[0]);
                        params.append('end_date', customDateRange.value[1]);
                    }

                    const response = await fetch(`/projectmanagement/api/stats_task_details?${params.toString()}`);
                    const data = await response.json();

                    if (data.success) {
                        statsTaskDialog.value.tasks = data.tasks;
                        statsTaskDialog.value.total = data.pagination.total;
                    } else {
                        throw new Error(data.message || '获取任务详情失败');
                    }
                } catch (error) {
                    console.error('获取任务详情失败:', error);
                    ElementPlus.ElMessage({
                        message: error.message || '获取任务详情失败',
                        type: 'error'
                    });
                } finally {
                    statsTaskDialog.value.loading = false;
                }
            };

            // 处理页码变化
            const handleStatsTaskPageChange = (newPage) => {
                statsTaskDialog.value.currentPage = newPage;
                loadStatsTaskData();
            };

            // 处理每页数量变化
            const handleStatsTaskPageSizeChange = (newSize) => {
                statsTaskDialog.value.pageSize = newSize;
                statsTaskDialog.value.currentPage = 1;
                loadStatsTaskData();
            };

                            return {
                    userInfo,
                    projects,
                    currentProject,
                    projectSearch,
                    filteredProjects,
                    filteredManageableProjects,
                    currentView,
                    searchQuery,
                    tasks,
                    navItems,
                    drawer,
                    pageTitle,
                    changeView,
                    selectProject,
                    searchTasks,
                    getRowClass,
                    getCompleteTitle,
                    handleRowClick,
                    closeDrawer,
                    completeTask,
                    sendReply,
                    goToProject,
                    getEmptyText,
                    // getTaskStatusLabel,
                    deleteTask,
                    tableLoading,
                    totalTasks,
                projectDialog,
                openProjectManagement,
                loadProjectMembers,
                loadAvailableUsers,
                addProjectMember,
                removeProjectMember,
                handleProjectDialogClosed,
                saveProjectSettings,
                showAddProjectDialog,
                handleRoleChange,
                getFilteredProjectMembers,
                deleteProject,
                handleProjectChange,
                isDeptSelected,
                // Template Management
                templates,
                templateLoading,
                templateSearch,
                filteredTemplates,
                loadTemplates,
                showAddTemplateDialog,
                deleteTemplate,
                templateEditDialog,
                showEditTemplateDialog,
                closeTemplateEditDialog,
                submitTemplateEdit,
                viewTemplate,
                currentTemplateDetails,
                templateTasks,
                templateTasksLoading,
                loadTemplateTasks,
                selectedTemplateTask,
                templateTaskDialog,
                handleTemplateTaskRowClick,
                showAddTemplateTaskDialog,
                showAddTemplateSubTaskDialog,
                showEditTemplateTaskDialog,
                closeTemplateTaskDialog,
                submitTemplateTask,
                deleteTemplateTask,
                moveTemplateTask,
                // 新增模板对话框
                addTemplateDialog,
                closeAddTemplateDialog,
                submitAddTemplate,
                // Home Features
                homeLoading,
                myTodoTasks,
                myCreatedTasks,
                activeTodoTab,
                activeCreatedTab,
                formattedDate,
                greeting,
                statsData,
                filteredTodoTasks,
                filteredCreatedTasks,
                loadHomeData,
                handleTabChange,
                statsTimeRange,
                customDateRange,
                handleStatsTimeRangeChange,
                handleCustomDateRangeChange,
                getStatsTimeRangeLabel,
                focusStats,
                filteredTodoStats,
                // Charts
                renderProjectStatCharts,
                // Task Handling
                taskDialog,
                showAddTaskDialog,
                showAddHistoricalTaskDialog,
                showAddSubTaskDialog,
                showEditTaskDialog,
                submitAddTask,
                closeTaskDialog,
                disablePastDates,
                taskFormRef,
                // Project Task Pagination
                pageSize,
                currentPage,
                paginatedTasks,
                handlePageChange,
                // Template Task Pagination
                currentTemplateTasksPage,
                templatePageSize,
                paginatedTemplateTasks,
                handleTemplateTasksPageChange,
                // Filtering
                hasActiveFilters,
                projectFilters,
                filterUrgency,
                filterProject,
                clearAllFilters,
                // Month Selector
                selectedMonth,
                selectedCreateMonth,
                handleMonthChange,
                // 任务状态筛选
                taskStatusFilter,
                handleTaskStatusChange,
                taskStatus,              
                statusFilters,
                projectTaskSearchQuery,
                handleProjectTaskSearch,
                homeApprovalBadgeCount,
                // Stats Date Range
                statsDateRange,
                statsCustomDateRange,
                disableFutureDates,
                handleStatsDateRangeChange,
                handleStatsCustomDateRangeChange,
                // 新增：返回模板列表函数
                goBackToTemplates,
                // 新增：导入模板占位函数
                showImportTemplateDialog,
                importTemplateDialog,
                closeImportTemplateDialog,
                importTemplate,
                getSelectedTemplateDescription,
                // 快速创建任务功能
                quickTaskName,
                quickTaskDeadline,
                quickTaskProjectId,
                quickTaskUrgency,
                handleQuickTaskDateChange,
                handleQuickTaskProjectChange,
                handleQuickTaskUrgencyChange,
                focusQuickTaskDate,
                createQuickTask,
                quickCreatedTaskName,
                quickCreatedTaskDeadline,
                quickCreatedTaskProjectId,
                quickCreatedTaskUrgency,
                handleQuickCreatedTaskDateChange,
                handleQuickCreatedTaskProjectChange,
                handleQuickCreatedTaskUrgencyChange,
                handleSelectAllQuickTaskAssignees,
                focusQuickCreatedTaskDate,
                createQuickCreatedTask,
                quickCreatedTaskAssignees,
                availableAssignees,
                handleQuickCreatedTaskAssigneeChange,
                // 新增：负责人搜索相关数据
                assigneeSearchQuery: homeFeatures.assigneeSearchQuery,
                filteredAssignees: homeFeatures.filteredAssignees,
                // 新增：项目搜索和过滤功能
                projectSearchQuery,
                filteredTaskProjects,
                // 添加任务删除审核相关功能
                projectDeleteSettings: taskDeleteApproval.projectDeleteSettings,
                approvalRequests: taskDeleteApproval.approvalRequests,
                approvalLoading: taskDeleteApproval.approvalLoading,
                approvalDetailDialog: taskDeleteApproval.approvalDetailDialog,
                updateProjectDeleteSettings: taskDeleteApproval.updateProjectDeleteSettings,
                approveTaskDelete: taskDeleteApproval.approveTaskDelete,
                rejectTaskDelete: taskDeleteApproval.rejectTaskDelete,
                loadPendingApprovals,
                showApprovalDetails: taskDeleteApproval.showApprovalDetails,
                approveFromDetail: taskDeleteApproval.approveFromDetail,
                rejectFromDetail: taskDeleteApproval.rejectFromDetail,
                // 添加任务状态标签函数
                getTaskStatusLabel: taskUtils.getTaskStatusLabel,
                completedSearchQuery,
                receivedTaskStatus,
                receivedSearchQuery,
                receivedTaskType,
                receivedSelectedMonth,
                handleReceivedMonthChange,
                toggleTaskTypeFilter,
                assigneeStats,
                loadAssigneeStats,
                assigneeStatsPageSize,
                assigneeStatsCurrentPage,
                assigneeStatsTotal,
                handleAssigneeStatsPageChange,
                handleAssigneeStatsPageSizeChange,
                assigneeNameFilter,
                assigneeUrgencyFilter,
                assigneeDateFilter,
                assigneeCustomDateRange,
                dateFilterOptions,
                handleAssigneeNameFilterChange,
                handleAssigneeStatsSearch,
                rowClassName,
                getTotalActiveTasks,
                getTotalOverdueTasks,
                // 新增：项目筛选相关
                assigneeProjectFilter,
                projectOptions,
                loadProjectOptions,
                // 新增：截至日期筛选相关
                assigneeDeadlineFilter,
                assigneeCustomDeadlineRange,
                deadlineFilterOptions,
                // 任务详情对话框相关
                taskDetailDialog,
                showTaskDetailDialog,
                closeTaskDetailDialog,
                handleTaskDetailPageChange,
                exportLoading,
                exportAssigneeStats,
                canSendReply,
                selectAllAssignees,
                handleSelectAllAssignees,
                isWithinDeleteTimeLimit,
                deleteLog,
                sendDingReply,
                // 新增：部门筛选相关
                assigneeDepartmentFilter,
                departments,
                loadDepartments,
                // 新增：组织架构树相关
                orgTreeDialog,
                showOrgTreeDialog,
                showAssigneeOrgTreeDialog, // 新增：负责人选择组织架构树
                handleDeptClick,
                toggleCcUser,
                isUserSelected,
                removeCcUser,
                confirmCcUsers,
                cancelCcUsers,
                navigateToDept,
                clearSelectedUsers,
                searchOrgMembers,
                handleSelectAllDept,
                handleSelectDept,
                selectDeptWithAllUsers,
                unselectDeptWithAllUsers,
                exportTaskDetailData,
                // 导出Excel相关
                exportProjectTasks,
                statsTaskDialog,
                handleStatsItemClick,
                handleStatsTaskPageChange,
                handleStatsTaskPageSizeChange,
            };
        }
    });

    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });
    
    // 注册所有Element Plus图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component);
    }
    
    // 挂载应用并暴露Vue实例到window对象
    const vueApp = app.mount('#app');
    window.vueApp = vueApp;
} catch (error) {
    console.error('初始化应用时发生错误:', error);
}