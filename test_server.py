#!/usr/bin/env python3
"""
简单的测试服务器，用于测试移动导航功能
"""

import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse

class TestHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头，允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # 如果请求根路径，重定向到测试页面
        if self.path == '/':
            self.path = '/mobile_navigation_test.html'
        
        return super().do_GET()

def start_test_server(port=8000):
    """启动测试服务器"""
    
    # 确保在正确的目录中
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 检查必要的文件是否存在
    required_files = [
        'mobile_navigation_test.html',
        'static/js/mobile_navigation.js',
        'static/css/mobile_layout.css'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("错误：以下必要文件不存在：")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    try:
        with socketserver.TCPServer(("", port), TestHTTPRequestHandler) as httpd:
            print(f"测试服务器启动成功！")
            print(f"本地访问地址: http://localhost:{port}")
            print(f"网络访问地址: http://0.0.0.0:{port}")
            print("\n测试说明：")
            print("1. 在移动设备或浏览器的移动模式下访问")
            print("2. 测试左滑返回、浏览器返回按钮等功能")
            print("3. 观察导航抽屉是否正确关闭")
            print("4. 检查是否有黑色遮罩残留")
            print("\n按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}')
            except:
                pass
            
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"错误：端口 {port} 已被占用，请尝试其他端口")
            return False
        else:
            print(f"启动服务器时出错：{e}")
            return False
    except KeyboardInterrupt:
        print("\n服务器已停止")
        return True

if __name__ == "__main__":
    import sys
    
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("错误：端口号必须是数字")
            sys.exit(1)
    
    success = start_test_server(port)
    sys.exit(0 if success else 1)
