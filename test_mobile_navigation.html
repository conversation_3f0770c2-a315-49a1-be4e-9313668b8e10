<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动导航测试页面</title>
    <link rel="stylesheet" href="static/css/mobile_layout.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #4682fa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #3a6fd8;
        }
        
        .status-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin: 12px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #2d3748;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 移动端菜单按钮 -->
    <div class="mobile-menu-toggle" id="menuToggle">
        <div class="menu-toggle">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-arrow">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        </div>
    </div>

    <!-- 移动端侧边导航抽屉 -->
    <div class="mobile-nav-drawer" id="mobileNavDrawer">
        <div class="mobile-nav-header">
            <div class="mobile-nav-user-info">
                <div class="mobile-nav-user-avatar" style="width: 40px; height: 40px; background: #4682fa; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">测</div>
                <span class="mobile-nav-username">测试用户</span>
            </div>
            <div class="mobile-nav-close" id="mobileNavClose">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </div>
        </div>
        
        <div class="mobile-nav-menu">
            <div class="mobile-nav-item" data-module-id="test1" data-has-children="true">
                <div class="mobile-nav-item-content">
                    <span class="mobile-nav-title">测试模块1</span>
                    <div class="mobile-nav-arrow">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>
                </div>
                <div class="mobile-subnav-container">
                    <div class="mobile-subnav-item" data-url="/test1">子项目1</div>
                    <div class="mobile-subnav-item" data-url="/test2">子项目2</div>
                </div>
            </div>
            
            <div class="mobile-nav-item" data-url="/test3">
                <div class="mobile-nav-item-content">
                    <span class="mobile-nav-title">测试模块2</span>
                </div>
            </div>
        </div>
        
        <div class="mobile-nav-footer">
            <button class="mobile-nav-logout">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                </svg>
                退出登录
            </button>
        </div>
    </div>
    
    <!-- 导航遮罩层 -->
    <div class="mobile-nav-overlay" id="mobileNavOverlay"></div>

    <div class="test-container">
        <h1>移动导航返回操作修复测试</h1>
        
        <div class="test-section">
            <h3>基本操作测试</h3>
            <button class="test-button" onclick="testOpen()">打开导航栏</button>
            <button class="test-button" onclick="testClose()">关闭导航栏</button>
            <button class="test-button" onclick="testToggle()">切换导航栏</button>
        </div>
        
        <div class="test-section">
            <h3>返回操作测试</h3>
            <button class="test-button" onclick="testBackNavigation()">模拟返回操作</button>
            <button class="test-button" onclick="testForceClose()">强制关闭</button>
            <button class="test-button" onclick="testEmergencyReset()">紧急恢复</button>
        </div>

        <div class="test-section">
            <h3>紧急恢复功能</h3>
            <button class="test-button" onclick="createEmergencyButton()">创建紧急按钮</button>
            <button class="test-button" onclick="simulateStuckState()">模拟卡住状态</button>
        </div>
        
        <div class="test-section">
            <h3>状态检查</h3>
            <button class="test-button" onclick="checkStatus()">检查当前状态</button>
            <button class="test-button" onclick="showLogs()">显示日志</button>
            <button class="test-button" onclick="clearLogs()">清除日志</button>
        </div>
        
        <div class="status-display" id="statusDisplay">
            状态信息将在这里显示...
        </div>
        
        <div class="log-container" id="logContainer">
            日志信息将在这里显示...
        </div>
    </div>

    <script src="static/js/mobile_navigation.js"></script>
    <script>
        function testOpen() {
            MobileNavigation.open();
            updateStatus('导航栏已打开');
        }
        
        function testClose() {
            MobileNavigation.close();
            updateStatus('导航栏已关闭');
        }
        
        function testToggle() {
            MobileNavigation.toggle();
            updateStatus('导航栏已切换');
        }
        
        function testBackNavigation() {
            MobileNavigation.testBackNavigationFix();
            updateStatus('返回操作测试已启动，请查看控制台');
        }
        
        function testForceClose() {
            MobileNavigation.forceClose();
            updateStatus('强制关闭已执行');
        }

        function testEmergencyReset() {
            MobileNavigation.emergencyReset();
            updateStatus('紧急恢复已执行');
        }

        function createEmergencyButton() {
            MobileNavigation.createEmergencyButton();
            updateStatus('紧急恢复按钮已创建（右上角红色按钮）');
        }

        function simulateStuckState() {
            // 模拟导航栏卡住的状态
            const drawer = document.getElementById('mobileNavDrawer');
            const overlay = document.getElementById('mobileNavOverlay');

            if (drawer) drawer.className = 'mobile-nav-drawer open';
            if (overlay) overlay.className = 'mobile-nav-overlay show';
            document.body.classList.add('nav-open');

            updateStatus('已模拟卡住状态，请使用紧急恢复功能');
        }
        
        function checkStatus() {
            const status = MobileNavigation.getNavigationStatus();
            updateStatus(JSON.stringify(status, null, 2));
        }
        
        function showLogs() {
            const logs = MobileNavigation.getNavigationLogs();
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = logs.map(log => 
                `[${log.time}] ${log.action}: ${JSON.stringify(log.details || {})}`
            ).join('\n');
        }
        
        function clearLogs() {
            MobileNavigation.clearNavigationLogs();
            document.getElementById('logContainer').innerHTML = '日志已清除';
            updateStatus('日志已清除');
        }
        
        function updateStatus(message) {
            document.getElementById('statusDisplay').textContent = message;
        }
        
        // 页面加载完成后显示初始状态
        window.addEventListener('load', function() {
            updateStatus('页面已加载，导航系统已初始化');
            showLogs();
        });
    </script>
</body>
</html>
