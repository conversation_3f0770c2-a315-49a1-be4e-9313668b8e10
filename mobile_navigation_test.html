<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动导航测试页面</title>
    <link rel="stylesheet" href="static/css/mobile_layout.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-content {
            padding: 20px;
            margin-left: 0;
            min-height: 100vh;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #4682fa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #3366cc;
        }
        
        .status-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .navigation-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .nav-link {
            background: #28a745;
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .nav-link:hover {
            background: #218838;
        }
        
        @media (max-width: 1023px) {
            .test-content {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端菜单按钮 -->
    <div class="mobile-menu-toggle" id="menuToggle">
        <div class="menu-toggle">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-arrow">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        </div>
    </div>

    <!-- 移动端侧边导航抽屉 -->
    <div class="mobile-nav-drawer" id="mobileNavDrawer">
        <div class="mobile-nav-header">
            <div class="mobile-nav-user-info">
                <div class="mobile-nav-user-avatar" style="width: 40px; height: 40px; border-radius: 50%; background: #4682fa; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">测</div>
                <span class="mobile-nav-username">测试用户</span>
            </div>
            <div class="mobile-nav-close" id="mobileNavClose">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </div>
        </div>
        
        <div class="mobile-nav-menu">
            <div class="mobile-nav-item" data-url="#home" data-has-children="false">
                <div class="mobile-nav-item-content">
                    <span class="mobile-nav-icon">🏠</span>
                    <span class="mobile-nav-title">首页</span>
                </div>
            </div>
            
            <div class="mobile-nav-item" data-url="#projects" data-has-children="true" data-module-id="project_management">
                <div class="mobile-nav-item-content">
                    <span class="mobile-nav-icon">📋</span>
                    <span class="mobile-nav-title">项目管理</span>
                    <div class="mobile-nav-arrow">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>
                </div>
                <div class="mobile-subnav-container">
                    <!-- 子导航项将由JavaScript动态生成 -->
                </div>
            </div>
            
            <div class="mobile-nav-item" data-url="#ai" data-has-children="true" data-module-id="ai_hub">
                <div class="mobile-nav-item-content">
                    <span class="mobile-nav-icon">🤖</span>
                    <span class="mobile-nav-title">AI助理</span>
                    <div class="mobile-nav-arrow">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>
                </div>
                <div class="mobile-subnav-container">
                    <!-- 子导航项将由JavaScript动态生成 -->
                </div>
            </div>
        </div>
        
        <div class="mobile-nav-footer">
            <button class="mobile-nav-logout">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                </svg>
                退出登录
            </button>
        </div>
    </div>
    
    <!-- 导航遮罩层 -->
    <div class="mobile-nav-overlay" id="mobileNavOverlay"></div>

    <!-- 主要内容区域 -->
    <div class="test-content">
        <div class="test-section">
            <h2>移动导航测试页面</h2>
            <p>这个页面用于测试移动导航的各种功能，特别是返回操作的处理。</p>
        </div>
        
        <div class="test-section">
            <h3>导航控制</h3>
            <button class="test-button" onclick="MobileNavigation.open()">打开导航</button>
            <button class="test-button" onclick="MobileNavigation.close()">关闭导航</button>
            <button class="test-button" onclick="MobileNavigation.toggle()">切换导航</button>
            <button class="test-button" onclick="MobileNavigation.forceClose()">强制关闭</button>
        </div>
        
        <div class="test-section">
            <h3>状态查询</h3>
            <button class="test-button" onclick="updateStatus()">更新状态</button>
            <button class="test-button" onclick="clearLogs()">清除日志</button>
            <div class="status-display" id="statusDisplay">点击"更新状态"查看当前状态</div>
        </div>
        
        <div class="test-section">
            <h3>导航日志</h3>
            <div class="status-display" id="logDisplay" style="max-height: 200px; overflow-y: auto;">
                导航操作日志将显示在这里
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试链接（用于测试返回功能）</h3>
            <div class="navigation-links">
                <a href="#page1" class="nav-link">页面1</a>
                <a href="#page2" class="nav-link">页面2</a>
                <a href="#page3" class="nav-link">页面3</a>
                <a href="javascript:history.back()" class="nav-link">返回上一页</a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>移动端加载动画测试</h3>
            <button class="test-button" onclick="testMobileLoading()">测试移动端加载动画</button>
            <button class="test-button" onclick="testSkeletonScreen()">测试骨架屏效果</button>
            <div id="mobileLoadingDemo" style="margin-top: 15px; min-height: 200px; border: 1px solid #ddd; border-radius: 8px; position: relative; background: #f9f9f9;"></div>
        </div>

        <div class="test-section">
            <h3>测试说明</h3>
            <ol>
                <li>点击左侧的三角形按钮打开导航</li>
                <li>尝试使用浏览器的返回按钮</li>
                <li>在支持的设备上尝试左滑返回</li>
                <li>观察导航是否正确关闭，遮罩层是否消失</li>
                <li>检查导航日志了解详细的操作记录</li>
                <li><strong>新增：测试移动端"我的关注"页面的加载动画效果</strong></li>
            </ol>
        </div>
    </div>

    <script src="static/js/mobile_navigation.js"></script>
    <script>
        // 测试页面的辅助函数
        function updateStatus() {
            const status = MobileNavigation.getState();
            const statusDisplay = document.getElementById('statusDisplay');
            statusDisplay.innerHTML = `
                <strong>导航状态:</strong><br>
                是否打开: ${status.isOpen}<br>
                是否过渡中: ${status.isTransitioning}<br>
                最后关闭时间: ${new Date(status.lastCloseTime).toLocaleTimeString()}<br>
                当前时间: ${new Date().toLocaleTimeString()}
            `;
        }
        
        function clearLogs() {
            MobileNavigation.clearNavigationLogs();
            updateLogs();
        }
        
        function updateLogs() {
            const logs = MobileNavigation.getNavigationLogs();
            const logDisplay = document.getElementById('logDisplay');
            
            if (logs.length === 0) {
                logDisplay.innerHTML = '暂无导航日志';
                return;
            }
            
            const logHtml = logs.slice(-20).map(log => {
                const time = new Date(log.time).toLocaleTimeString();
                return `<div class="log-entry">[${time}] ${log.action}: ${JSON.stringify(log.details)}</div>`;
            }).join('');
            
            logDisplay.innerHTML = logHtml;
            logDisplay.scrollTop = logDisplay.scrollHeight;
        }
        
        // 定期更新日志显示
        setInterval(updateLogs, 1000);
        
        // 监听URL变化
        window.addEventListener('hashchange', function() {
            console.log('URL changed to:', window.location.hash);
            updateStatus();
        });
        
        // 测试移动端加载动画
        function testMobileLoading() {
            const demoContainer = document.getElementById('mobileLoadingDemo');
            demoContainer.innerHTML = `
                <div class="mobile-loading-container">
                    <div class="mobile-loading-content">
                        <div class="mobile-loading-spinner">
                            <svg class="loading-spinner" viewBox="0 0 24 24" width="32" height="32" style="color: #409eff;">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                    <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                                </svg>
                            </div>
                            <div class="mobile-loading-text">正在加载任务数据...</div>
                        </div>
                    </div>
                </div>
            `;

            // 3秒后清除
            setTimeout(() => {
                demoContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #67c23a;">✓ 加载完成！</div>';
            }, 3000);
        }

        // 测试骨架屏效果
        function testSkeletonScreen() {
            const demoContainer = document.getElementById('mobileLoadingDemo');
            demoContainer.innerHTML = `
                <div style="padding: 15px;">
                    <div style="margin-bottom: 10px; font-weight: bold; color: #606266;">骨架屏效果预览：</div>
                    <div class="mobile-loading-skeleton">
                        <div class="skeleton-card">
                            <div class="skeleton-header">
                                <div class="skeleton-checkbox"></div>
                                <div class="skeleton-title"></div>
                            </div>
                            <div class="skeleton-tags">
                                <div class="skeleton-tag"></div>
                                <div class="skeleton-tag"></div>
                                <div class="skeleton-tag"></div>
                            </div>
                            <div class="skeleton-info">
                                <div class="skeleton-info-item"></div>
                                <div class="skeleton-info-item"></div>
                            </div>
                        </div>
                        <div class="skeleton-card">
                            <div class="skeleton-header">
                                <div class="skeleton-checkbox"></div>
                                <div class="skeleton-title"></div>
                            </div>
                            <div class="skeleton-tags">
                                <div class="skeleton-tag"></div>
                                <div class="skeleton-tag"></div>
                            </div>
                            <div class="skeleton-info">
                                <div class="skeleton-info-item"></div>
                                <div class="skeleton-info-item"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加骨架屏样式
            const style = document.createElement('style');
            style.textContent = `
                .mobile-loading-skeleton { display: flex; flex-direction: column; gap: 15px; }
                .skeleton-card { background: #fff; border-radius: 12px; padding: 15px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); border: 1px solid #f0f0f0; }
                .skeleton-header { display: flex; align-items: center; gap: 10px; margin-bottom: 12px; }
                .skeleton-checkbox { width: 16px; height: 16px; border-radius: 3px; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite; }
                .skeleton-title { flex: 1; height: 18px; border-radius: 4px; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite; }
                .skeleton-tags { display: flex; gap: 8px; margin-bottom: 12px; }
                .skeleton-tag { width: 60px; height: 24px; border-radius: 12px; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite; }
                .skeleton-info { display: flex; gap: 15px; }
                .skeleton-info-item { width: 80px; height: 14px; border-radius: 3px; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite; }
                @keyframes skeleton-loading { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }
            `;
            document.head.appendChild(style);

            // 3秒后清除
            setTimeout(() => {
                demoContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #67c23a;">✓ 骨架屏演示完成！</div>';
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            updateLogs();
        });
    </script>
</body>
</html>
