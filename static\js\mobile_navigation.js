/**
 * 移动端导航交互脚本 - 完全修复版本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const menuToggle = document.getElementById('menuToggle');
    const mobileNavDrawer = document.getElementById('mobileNavDrawer');
    const mobileNavClose = document.getElementById('mobileNavClose');
    const mobileNavOverlay = document.getElementById('mobileNavOverlay');
    let mobileNavItems = document.querySelectorAll('.mobile-nav-item');

    // 状态管理
    let isInitialized = false;
    let resizeTimer = null;
    // 导航日志功能
    const enableLogging = true; // 开启导航日志记录

    // 记录导航操作的函数
    function logNavigation(action, details) {
        if (!enableLogging) return;
        const timestamp = new Date().toISOString();
        const log = {
            time: timestamp,
            action: action,
            details: details,
            url: window.location.href
        };
        
        // 记录导航操作到会话存储，便于调试
        try {
            const logs = JSON.parse(sessionStorage.getItem('navLogs') || '[]');
            logs.push(log);
            // 只保留最近50条记录
            if (logs.length > 50) logs.shift();
            sessionStorage.setItem('navLogs', JSON.stringify(logs));
        } catch (e) {
            console.warn('无法保存导航日志到会话存储:', e);
        }
    }

    // 定义导航配置，包括所有可能的二级导航项
    const navigationConfig = {
        // 任务管理模块
        'project_management': [
            { id: 'pm-home', title: '任务管理首页', url: '/projectmanagement/main?view_type=home' },
            { id: 'pm-received', title: '我的关注', url: '/projectmanagement/main?view_type=received' }
        ],
        // 大象币板块
        'reward_coin': [
            { id: 'rc-issue', title: '大象币发放', url: '/reward_coin?tab=issue', tab: 'issue' },
            { id: 'rc-received', title: '大象币接收', url: '/reward_coin?tab=received', tab: 'received' }
        ],
        // AI助理中心
        'ai_hub': [
            { id: 'ai-home', title: 'AI助理首页', url: '/ai_hub_layout' },
            { id: 'ai-gemini', title: '谷歌Gemini', url: '/ai_hub_layout?site=gemini' }
        ],
        // 其他模块
        'document_center': [
            { id: 'doc-home', title: '文档中心首页', url: '/document_center' },
            { id: 'doc-personal', title: '我的文档', url: '/document_center?view=personal' },
            { id: 'doc-shared', title: '共享文档', url: '/document_center?view=shared' }
        ],
        'data_analysis': [
            { id: 'data-dashboard', title: '数据概览', url: '/data_analysis' },
            { id: 'data-reports', title: '数据报表', url: '/data_analysis?view=reports' },
            { id: 'data-custom', title: '自定义分析', url: '/data_analysis?view=custom' }
        ]
    };

    /**
     * 安全的localStorage操作
     */
    const StorageManager = {
        /**
         * 保存导航状态
         */
        saveNavigationState: function(moduleId, isExpanded) {
            if (!moduleId) return false;
            try {
                const navState = JSON.parse(localStorage.getItem('navState') || '{}');
                navState[moduleId] = isExpanded;
                localStorage.setItem('navState', JSON.stringify(navState));
                return true;
            } catch (error) {
                console.warn('保存导航状态失败:', error);
                return false;
            }
        },

        /**
         * 获取导航状态
         */
        getNavigationState: function() {
            try {
                return JSON.parse(localStorage.getItem('navState') || '{}');
            } catch (error) {
                console.warn('读取导航状态失败:', error);
                return {};
            }
        },

        /**
         * 清除特定模块的导航状态
         */
        clearNavigationState: function(moduleId) {
            if (!moduleId) return false;
            try {
                const navState = this.getNavigationState();
                delete navState[moduleId];
                localStorage.setItem('navState', JSON.stringify(navState));
                return true;
            } catch (error) {
                console.warn('清除导航状态失败:', error);
                return false;
            }
        }
    };

    /**
     * 工具函数
     */
    const Utils = {
        /**
         * 防抖函数
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * 安全地执行DOM操作
         */
        safeQuerySelector: function(selector, parent = document) {
            try {
                return parent.querySelector(selector);
            } catch (error) {
                console.warn(`查询选择器失败: ${selector}`, error);
                return null;
            }
        },

        /**
         * 批量添加CSS类
         */
        addClasses: function(element, classes) {
            if (!element || !classes) return false;
            try {
                const classArray = Array.isArray(classes) ? classes : [classes];
                element.classList.add(...classArray);
                return true;
            } catch (error) {
                console.warn('添加CSS类失败:', error);
                return false;
            }
        },

        /**
         * 批量移除CSS类
         */
        removeClasses: function(element, classes) {
            if (!element || !classes) return false;
            try {
                const classArray = Array.isArray(classes) ? classes : [classes];
                element.classList.remove(...classArray);
                return true;
            } catch (error) {
                console.warn('移除CSS类失败:', error);
                return false;
            }
        }
    };

    /**
     * 处理窗口大小变化 - 防抖优化
     */
    const handleResize = Utils.debounce(function() {
        adjustNavDrawerHeight();
    }, 150);

    /**
     * 检查导航项是否应该标记为活动状态 - 严格匹配版
     */
    function checkActiveState(element, url) {
        if (!url || !element) return false;
        try {
            // 获取当前URL（不包含origin）
            const currentUrl = window.location.pathname + window.location.search;
            const targetUrl = new URL(url, window.location.origin);
            const targetPath = targetUrl.pathname;
            const targetSearch = targetUrl.search;
            let isActive = false;

            // 只进行精确匹配，不再进行部分匹配
            if (currentUrl === targetPath + targetSearch) {
                // 特殊处理：如果是退出登录链接，永远不标记为active
                if (targetPath.includes('logout') || targetPath.includes('sign_out')) {
                    isActive = false;
                } else {
                    isActive = true;
                }
            }

            // 设置活动状态
            element.classList.toggle('active', isActive);
            return isActive;
        } catch (error) {
            console.warn('检查活动状态错误:', error);
            return false;
        }
    }

    /**
     * 检查当前URL是否属于某个模块
     */
    function checkUrlBelongsToModule(moduleId) {
        if (!moduleId || !navigationConfig[moduleId]) return false;
        try {
            const currentUrl = window.location.pathname + window.location.search;
            // 检查模块的所有子页面URL
            for (let i = 0; i < navigationConfig[moduleId].length; i++) {
                const subItem = navigationConfig[moduleId][i];
                const targetUrl = new URL(subItem.url, window.location.origin);
                const targetPath = targetUrl.pathname;
                const targetSearch = targetUrl.search;

                // 进行精确匹配
                if (currentUrl === targetPath + targetSearch) {
                    logNavigation('URL匹配模块', {
                        moduleId: moduleId,
                        url: currentUrl,
                        matchType: '精确匹配',
                        targetUrl: targetPath + targetSearch
                    });
                    return true;
                }

                // 对于特定模块，可以添加更宽松的匹配规则
                // 例如：检查URL是否以模块路径开头
                if (targetPath && currentUrl.startsWith(targetPath)) {
                    // 对于带有tab参数的URL，需要额外检查
                    if (subItem.tab) {
                        const urlParams = new URLSearchParams(window.location.search);
                        const tabValue = urlParams.get('tab');
                        if (tabValue === subItem.tab) {
                            logNavigation('URL匹配模块', {
                                moduleId: moduleId,
                                url: currentUrl,
                                matchType: '路径前缀+tab参数匹配',
                                targetUrl: targetPath,
                                tab: subItem.tab
                            });
                            return true;
                        }
                    } else {
                        logNavigation('URL匹配模块', {
                            moduleId: moduleId,
                            url: currentUrl,
                            matchType: '路径前缀匹配',
                            targetUrl: targetPath
                        });
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            console.warn('检查URL模块错误:', error);
            return false;
        }
    }

    /**
     * 动态更新导航状态 - 优化版本
     */
    function updateActiveNavigation() {
        // 检查是否是通过返回按钮触发的
        const isBackNavigation = performance && performance.getEntriesByType && 
            performance.getEntriesByType("navigation").length > 0 && 
            performance.getEntriesByType("navigation")[0].type === "back_forward";
            
        // 先重置所有导航项状态
        mobileNavItems.forEach(function(item) {
            const moduleId = item.getAttribute('data-module-id');
            const url = item.getAttribute('data-url');
            
            // 检查一级导航项是否匹配当前URL
            const isDirectMatch = checkActiveState(item, url);

            // 如果不是直接匹配，则检查是否有子项匹配
            if (!isDirectMatch && moduleId && navigationConfig[moduleId]) {
                // 检查当前URL是否属于此模块
                const belongsToModule = checkUrlBelongsToModule(moduleId);
                
                if (belongsToModule && !isBackNavigation) {
                    // 如果当前URL属于此模块且不是返回操作，展开该模块
                    expandNavigationItem(item, moduleId, false); // 不保存状态，因为这是基于URL的自动展开
                    
                    // 更新子导航项的活动状态
                    const subNavItems = item.querySelectorAll('.mobile-subnav-item');
                    subNavItems.forEach(function(subItem) {
                        const subUrl = subItem.getAttribute('data-url');
                        checkActiveState(subItem, subUrl);
                    });
                } else {
                    // 如果不属于此模块或是返回操作，检查是否需要折叠
                    const isCurrentlyExpanded = item.classList.contains('expanded');
                    if (isCurrentlyExpanded) {
                        // 折叠当前展开的项目
                        collapseNavigationItem(item, moduleId, false);
                    }
                }
            }
        });
        
        // 记录导航状态更新
        logNavigation('导航状态更新', {
            isBackNavigation: isBackNavigation,
            url: window.location.href
        });
    }

    /**
     * 展开导航项 - 新增独立函数
     */
    function expandNavigationItem(item, moduleId, saveState = true) {
        if (!item) return false;
        
        try {
            Utils.addClasses(item, 'expanded');
            const subNavContainer = item.querySelector('.mobile-subnav-container');
            
            if (subNavContainer) {
                // 先设置overflow为hidden防止动画过程中出现滚动条
                subNavContainer.style.overflowY = 'hidden';
                
                requestAnimationFrame(() => {
                    subNavContainer.style.maxHeight = subNavContainer.scrollHeight + 'px';
                    
                    // 动画完成后再设置overflow为auto
                    setTimeout(() => {
                        // 只有当元素仍然展开时才设置为auto
                        if(item.classList.contains('expanded')) {
                            subNavContainer.style.overflowY = 'auto';
                        }
                    }, 300); // 与CSS过渡时间匹配
                });
            }

            // 保存状态到localStorage
            if (saveState && moduleId) {
                StorageManager.saveNavigationState(moduleId, true);
            }
            
            return true;
        } catch (error) {
            console.warn('展开导航项失败:', error);
            return false;
        }
    }

    /**
     * 折叠导航项 - 新增独立函数
     */
    function collapseNavigationItem(item, moduleId, saveState = true) {
        if (!item) return false;
        
        try {
            Utils.removeClasses(item, 'expanded');
            const subNavContainer = item.querySelector('.mobile-subnav-container');
            
            if (subNavContainer) {
                // 立即设置overflow为hidden，避免折叠过程中出现滚动条
                subNavContainer.style.overflowY = 'hidden';
                // 使用requestAnimationFrame确保样式变化顺序正确
                requestAnimationFrame(() => {
                    subNavContainer.style.maxHeight = '0';
                });
            }

            // 保存状态到localStorage
            if (saveState && moduleId) {
                StorageManager.saveNavigationState(moduleId, false);
            }
            
            return true;
        } catch (error) {
            console.warn('折叠导航项失败:', error);
            return false;
        }
    }

    /**
     * 创建子导航项 - 修复版本
     */
    function createSubNavigationItem(subItem, parentItem, moduleId) {
        const subNavItem = document.createElement('div');
        subNavItem.className = 'mobile-subnav-item';
        subNavItem.setAttribute('data-url', subItem.url);
        subNavItem.textContent = subItem.title;

        // 检查当前URL是否匹配子菜单项URL
        if (checkActiveState(subNavItem, subItem.url)) {
            // 如果子菜单项激活，标记父菜单为展开状态
            expandNavigationItem(parentItem, moduleId, true);
        }

        return subNavItem;
    }

    /**
     * 绑定子导航项事件 - 优化版本
     */
    function bindSubNavItemEvents(subNavItem) {
        if (!subNavItem) return false;
        
        try {
            // 移除可能存在的旧事件监听器
            const newSubNavItem = subNavItem.cloneNode(true);
            if (subNavItem.parentNode) {
                subNavItem.parentNode.replaceChild(newSubNavItem, subNavItem);
            }

            // 为新元素绑定点击事件
            newSubNavItem.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const url = newSubNavItem.getAttribute('data-url');
                if (url) {
                    // 添加点击反馈
                    Utils.addClasses(newSubNavItem, 'navigating');
                    
                    // 记录导航操作
                    logNavigation('子导航项点击', {
                        url: url,
                        text: newSubNavItem.textContent,
                        userAgent: navigator.userAgent
                    });
                    
                    // 减少延迟时间，从150ms减少到50ms
                    setTimeout(() => {
                        window.location.href = url;
                    }, 50);
                } else {
                    console.warn('子导航项缺少URL属性');
                }
            });

            return newSubNavItem;
        } catch (error) {
            console.warn('绑定子导航项事件失败:', error);
            return subNavItem;
        }
    }

    /**
     * 显示消息提示 - 优化版
     */
    function showMessage(message) {
        const messageText = message || '该功能即将推出，敬请期待！';
        
        // 移除现有消息
        const existingMessage = document.getElementById('mobileMessage');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建新消息元素
        const messageEl = document.createElement('div');
        messageEl.id = 'mobileMessage';
        messageEl.className = 'mobile-message';
        messageEl.textContent = messageText;
        messageEl.setAttribute('role', 'alert');
        messageEl.setAttribute('aria-live', 'polite');

        // 添加到DOM
        document.body.appendChild(messageEl);

        // 使用requestAnimationFrame确保样式应用后再显示
        requestAnimationFrame(() => {
            Utils.addClasses(messageEl, 'visible');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                Utils.removeClasses(messageEl, 'visible');
                // 动画结束后移除元素
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 300);
            }, 3000);
        });
    }

    /**
     * 处理导航项点击事件
     */
    function handleNavigationItemClick(e, item) {
        const hasChildren = item.getAttribute('data-has-children') === 'true';
        const isComingSoon = item.getAttribute('data-coming-soon') === 'true';
        const url = item.getAttribute('data-url');
        const message = item.getAttribute('data-message');
        const moduleId = item.getAttribute('data-module-id');

        // 记录导航点击
        logNavigation('导航项点击', {
            moduleId: moduleId,
            url: url,
            hasChildren: hasChildren,
            isComingSoon: isComingSoon
        });

        if (isComingSoon) {
            // 显示"即将推出"消息
            showMessage(message);
            e.preventDefault();
            return;
        }

        if (hasChildren) {
            // 如果有子菜单，则展开/折叠子菜单
            toggleSubNavigation(item);
            e.preventDefault();
        } else {
            // 如果没有子菜单，则导航到对应URL
            if (url) {
                // 延迟导航，提供视觉反馈，减少延迟时间
                Utils.addClasses(item, 'navigating');
                setTimeout(() => {
                    window.location.href = url;
                }, 50); // 从150ms减少到50ms
            }
        }
    }

    /**
     * 绑定导航项点击事件 - 修复版本
     */
    function bindNavigationItemEvents() {
        // 重新获取最新的导航项
        mobileNavItems = document.querySelectorAll('.mobile-nav-item');
        
        mobileNavItems.forEach(function(item, index) {
            // 只为主导航项绑定事件，不处理子项
            const newItem = item.cloneNode(true);
            if (item.parentNode) {
                item.parentNode.replaceChild(newItem, item);
            }
            
            // 为新元素绑定事件
            newItem.addEventListener('click', function(e) {
                handleNavigationItemClick(e, newItem);
            });

            // 重新绑定子导航项事件
            const subNavItems = newItem.querySelectorAll('.mobile-subnav-item');
            subNavItems.forEach(function(subNavItem) {
                bindSubNavItemEvents(subNavItem);
            });
            
            // 更新引用
            mobileNavItems[index] = newItem;
        });
    }

    /**
     * 强制关闭导航抽屉 - 用于处理返回操作
     */
    function forceCloseNavDrawer() {
        if (!mobileNavDrawer) return false;

        try {
            // 记录强制关闭操作
            logNavigation('强制关闭导航抽屉', {
                reason: '用户返回操作',
                timestamp: new Date().toISOString()
            });

            // 立即移除所有相关的CSS类并添加强制隐藏类
            Utils.removeClasses(mobileNavDrawer, ['open', 'opening', 'closing']);
            Utils.addClasses(mobileNavDrawer, 'force-hidden');
            Utils.removeClasses(document.body, 'nav-open');

            // 立即隐藏遮罩层
            if (mobileNavOverlay) {
                Utils.removeClasses(mobileNavOverlay, 'visible');
                Utils.addClasses(mobileNavOverlay, 'force-hidden');
            }

            // 重置菜单按钮状态
            if (menuToggle) {
                Utils.removeClasses(menuToggle, 'active');

                // 恢复箭头方向
                const navArrow = menuToggle.querySelector('.nav-arrow polyline');
                if (navArrow) {
                    navArrow.setAttribute('points', '9 18 15 12 9 6');
                }
            }

            // 短暂延迟后移除强制隐藏类，恢复正常状态
            setTimeout(() => {
                if (mobileNavDrawer) {
                    Utils.removeClasses(mobileNavDrawer, 'force-hidden');
                }
                if (mobileNavOverlay) {
                    Utils.removeClasses(mobileNavOverlay, 'force-hidden');
                }
            }, 200);

            return true;
        } catch (error) {
            console.warn('强制关闭导航抽屉失败:', error);
            return false;
        }
    }

    /**
     * 检测是否为返回操作
     */
    function isBackNavigation() {
        try {
            // 检查performance API
            if (performance && performance.getEntriesByType) {
                const navEntries = performance.getEntriesByType("navigation");
                if (navEntries.length > 0) {
                    return navEntries[0].type === "back_forward";
                }
            }

            // 备用检测方法：检查history状态
            if (history && history.state) {
                return history.state.isBack === true;
            }

            return false;
        } catch (error) {
            console.warn('检测返回操作失败:', error);
            return false;
        }
    }

    /**
     * 处理页面可见性变化
     */
    function handleVisibilityChange() {
        // 当页面变为不可见时（如切换应用），关闭导航抽屉
        if (document.hidden && mobileNavDrawer && mobileNavDrawer.classList.contains('open')) {
            forceCloseNavDrawer();
        }
    }

    /**
     * 绑定所有事件监听器
     */
    function bindEventListeners() {
        // 监听窗口大小变化 - 使用防抖优化
        window.addEventListener('resize', handleResize);

        // 打开导航抽屉
        if (menuToggle) {
            menuToggle.addEventListener('click', openNavDrawer);
        }

        // 关闭导航抽屉
        if (mobileNavClose) {
            mobileNavClose.addEventListener('click', closeNavDrawer);
        }

        // 点击遮罩层关闭导航抽屉
        if (mobileNavOverlay) {
            mobileNavOverlay.addEventListener('click', closeNavDrawer);
        }

        // 导航项点击事件
        bindNavigationItemEvents();

        // 监听URL变化 - 增强版popstate处理
        window.addEventListener('popstate', function(event) {
            // 记录popstate事件
            logNavigation('popstate事件触发', {
                isBackNavigation: isBackNavigation(),
                state: event.state,
                url: window.location.href
            });

            // 如果导航抽屉是打开的，立即强制关闭
            if (mobileNavDrawer && mobileNavDrawer.classList.contains('open')) {
                forceCloseNavDrawer();
            }

            // 延迟更新导航状态，确保DOM更新完成
            setTimeout(updateActiveNavigation, 150);
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // 监听页面焦点事件
        window.addEventListener('focus', function() {
            // 当页面重新获得焦点时，检查导航状态
            if (mobileNavDrawer && mobileNavDrawer.classList.contains('open')) {
                // 如果导航抽屉意外打开，关闭它
                setTimeout(forceCloseNavDrawer, 100);
            }
        });

        // 监听页面失去焦点事件
        window.addEventListener('blur', function() {
            // 当页面失去焦点时，关闭导航抽屉
            if (mobileNavDrawer && mobileNavDrawer.classList.contains('open')) {
                forceCloseNavDrawer();
            }
        });

        // 监听手势事件（针对移动设备）
        if ('ontouchstart' in window) {
            // 监听触摸开始事件，用于检测手势
            let touchStartX = 0;
            let touchStartTime = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartX = e.touches[0].clientX;
                touchStartTime = Date.now();
            }, { passive: true });

            document.addEventListener('touchend', function(e) {
                const touchEndX = e.changedTouches[0].clientX;
                const touchDuration = Date.now() - touchStartTime;
                const touchDistance = touchEndX - touchStartX;

                // 检测左滑手势（从左边缘开始的右滑）
                if (touchStartX < 20 && touchDistance > 50 && touchDuration < 300) {
                    // 如果导航抽屉未打开，这可能是系统返回手势
                    if (!mobileNavDrawer || !mobileNavDrawer.classList.contains('open')) {
                        // 延迟检查是否需要关闭导航抽屉
                        setTimeout(() => {
                            if (mobileNavDrawer && mobileNavDrawer.classList.contains('open')) {
                                forceCloseNavDrawer();
                            }
                        }, 100);
                    }
                }
            }, { passive: true });
        }

        // 监听hashchange事件（某些单页应用可能使用hash路由）
        window.addEventListener('hashchange', function() {
            if (mobileNavDrawer && mobileNavDrawer.classList.contains('open')) {
                forceCloseNavDrawer();
            }
        });
    }

    /**
     * 恢复导航状态
     */
    function restoreNavigationState() {
        const navState = StorageManager.getNavigationState();
        const itemsToExpand = [];

        mobileNavItems.forEach(item => {
            const moduleId = item.getAttribute('data-module-id');
            if (moduleId && navState[moduleId]) {
                Utils.addClasses(item, 'expanded');
                const subNavContainer = item.querySelector('.mobile-subnav-container');
                if (subNavContainer) {
                    // 先设置overflow为hidden防止滚动条闪现
                    subNavContainer.style.overflowY = 'hidden';
                    itemsToExpand.push({container: subNavContainer, item: item});
                }
            }
        });

        // 批量展开，优化性能
        if (itemsToExpand.length > 0) {
            requestAnimationFrame(() => {
                itemsToExpand.forEach(({container, item}) => {
                    container.style.maxHeight = container.scrollHeight + 'px';
                    
                    // 动画完成后再设置overflow为auto
                    setTimeout(() => {
                        // 确认元素仍然处于展开状态
                        if (item.classList.contains('expanded')) {
                            container.style.overflowY = 'auto';
                        }
                    }, 300); // 与CSS过渡时间匹配
                });
            });
        }
    }

    /**
     * 调整导航抽屉高度以适应屏幕
     */
    function adjustNavDrawerHeight() {
        if (!mobileNavDrawer) return false;
        
        try {
            // 获取视口高度
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
            mobileNavDrawer.style.height = viewportHeight + 'px';

            // 确保底部导航在视口内
            const navMenu = Utils.safeQuerySelector('.mobile-nav-menu', mobileNavDrawer);
            const navHeader = Utils.safeQuerySelector('.mobile-nav-header', mobileNavDrawer);
            const navFooter = Utils.safeQuerySelector('.mobile-nav-footer', mobileNavDrawer);

            if (navMenu && navHeader && navFooter) {
                const headerHeight = navHeader.offsetHeight || 0;
                const footerHeight = navFooter.offsetHeight || 0;
                const availableHeight = viewportHeight - headerHeight - footerHeight;
                navMenu.style.maxHeight = Math.max(0, availableHeight) + 'px';
            }
            
            return true;
        } catch (error) {
            console.warn('调整导航抽屉高度失败:', error);
            return false;
        }
    }

    /**
     * 初始化二级导航 - 修复版本
     */
    function initSubNavigation() {
        const containersToExpand = [];

        mobileNavItems.forEach(function(item) {
            const hasChildren = item.getAttribute('data-has-children') === 'true';
            const moduleId = item.getAttribute('data-module-id');
            const url = item.getAttribute('data-url');

            // 检查当前URL是否匹配一级导航项URL
            checkActiveState(item, url);

            if (hasChildren && moduleId && navigationConfig[moduleId]) {
                const subNavContainer = item.querySelector('.mobile-subnav-container');
                if (subNavContainer && navigationConfig[moduleId].length > 0) {
                    // 清空现有内容
                    subNavContainer.innerHTML = '';
                    
                    // 使用文档片段优化DOM操作
                    const fragment = document.createDocumentFragment();

                    navigationConfig[moduleId].forEach(function(subItem) {
                        const subNavItem = createSubNavigationItem(subItem, item, moduleId);
                        // 绑定子导航项事件
                        const boundSubNavItem = bindSubNavItemEvents(subNavItem);
                        fragment.appendChild(boundSubNavItem);
                    });

                    // 添加新内容
                    subNavContainer.appendChild(fragment);

                    // 检查是否需要展开
                    if (item.classList.contains('expanded')) {
                        containersToExpand.push(subNavContainer);
                    }
                }
            }
        });

        // 恢复导航状态
        restoreNavigationState();
    }

    /**
     * 展开/折叠二级导航 - 核心BUG修复版本
     */
    function toggleSubNavigation(item) {
        if (!item) return false;

        const isCurrentlyExpanded = item.classList.contains('expanded');
        const moduleId = item.getAttribute('data-module-id');

        // 记录详细的切换状态
        logNavigation('切换子导航', {
            moduleId: moduleId,
            currentState: isCurrentlyExpanded ? '展开' : '折叠',
            action: isCurrentlyExpanded ? '折叠' : '展开'
        });

        try {
            // 关键修复：首先检查是否为当前展开的项
            if (isCurrentlyExpanded) {
                // 如果当前项已展开，直接折叠它
                console.log('折叠当前展开的导航项:', moduleId);
                collapseNavigationItem(item, moduleId, true);
                return true;
            }

            // 移除自动折叠其他项的逻辑，允许多个导航项同时展开
            // 直接展开当前项
            console.log('展开当前导航项:', moduleId);
            expandNavigationItem(item, moduleId, true);
            
            return true;
        } catch (error) {
            console.warn('切换子导航失败:', error);
            logNavigation('切换子导航失败', {
                moduleId: moduleId,
                error: error.message
            });
            return false;
        }
    }

    /**
     * 打开导航抽屉 - 优化版
     */
    function openNavDrawer() {
        if (!mobileNavDrawer || !mobileNavOverlay) return false;

        // 防止重复操作
        if (mobileNavDrawer.classList.contains('open')) return false;

        try {
            // 先调整高度，确保在打开前已经适应屏幕
            adjustNavDrawerHeight();

            // 在打开导航抽屉前更新导航状态
            updateActiveNavigation();

            // 添加加载状态
            Utils.addClasses(mobileNavDrawer, 'opening');

            // 使用requestAnimationFrame确保平滑动画
            requestAnimationFrame(() => {
                Utils.removeClasses(mobileNavDrawer, 'opening');
                Utils.addClasses(mobileNavDrawer, 'open');
                Utils.addClasses(mobileNavOverlay, 'visible');
                Utils.addClasses(document.body, 'nav-open');

                // 添加菜单按钮动画效果
                if (menuToggle) {
                    Utils.addClasses(menuToggle, 'active');
                    
                    // 翻转箭头方向 - 改为指向左侧
                    const navArrow = menuToggle.querySelector('.nav-arrow polyline');
                    if (navArrow) {
                        navArrow.setAttribute('points', '15 18 9 12 15 6');
                    }
                }
            });
            
            return true;
        } catch (error) {
            console.warn('打开导航抽屉失败:', error);
            return false;
        }
    }

    /**
     * 关闭导航抽屉 - 增强版
     */
    function closeNavDrawer() {
        if (!mobileNavDrawer || !mobileNavOverlay) return false;

        // 防止重复操作
        if (!mobileNavDrawer.classList.contains('open')) return false;

        try {
            // 记录关闭操作
            logNavigation('导航抽屉关闭开始', {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                method: '正常关闭'
            });

            // 添加关闭状态类，用于动画
            Utils.addClasses(mobileNavDrawer, 'closing');
            Utils.removeClasses(mobileNavDrawer, 'open');

            // 立即移除遮罩层可见性，防止遮罩层残留
            Utils.removeClasses(mobileNavOverlay, 'visible');
            Utils.removeClasses(document.body, 'nav-open');

            // 确保遮罩层完全隐藏 - 增强版
            mobileNavOverlay.style.opacity = '0';
            mobileNavOverlay.style.visibility = 'hidden';
            mobileNavOverlay.style.pointerEvents = 'none';
            mobileNavOverlay.style.zIndex = '-1';

            // 移除菜单按钮动画效果
            if (menuToggle) {
                Utils.removeClasses(menuToggle, 'active');

                // 恢复箭头方向 - 改为指向右侧
                const navArrow = menuToggle.querySelector('.nav-arrow polyline');
                if (navArrow) {
                    navArrow.setAttribute('points', '9 18 15 12 9 6');
                }

                // 恢复焦点到菜单按钮（但不在移动设备上）
                if (!('ontouchstart' in window)) {
                    menuToggle.focus();
                }
            }

            // 在动画结束后移除关闭状态类
            setTimeout(() => {
                Utils.removeClasses(mobileNavDrawer, 'closing');

                // 完全重置遮罩层状态
                if (mobileNavOverlay) {
                    mobileNavOverlay.style.display = 'none';

                    // 短暂延迟后恢复默认属性，确保下次可以正常显示
                    setTimeout(() => {
                        if (mobileNavOverlay) {
                            mobileNavOverlay.style.display = '';
                            mobileNavOverlay.style.opacity = '';
                            mobileNavOverlay.style.visibility = '';
                            mobileNavOverlay.style.pointerEvents = '';
                            mobileNavOverlay.style.zIndex = '';
                        }
                    }, 100);
                }

                // 记录关闭完成
                logNavigation('导航抽屉关闭完成', {
                    timestamp: new Date().toISOString()
                });
            }, 300); // 与CSS过渡时间匹配

            return true;
        } catch (error) {
            console.warn('关闭导航抽屉失败:', error);
            logNavigation('导航抽屉关闭失败', {
                error: error.message,
                stack: error.stack
            });

            // 如果正常关闭失败，尝试强制关闭
            return forceCloseNavDrawer();
        }
    }

    /**
     * 检查是否需要自动展开导航抽屉
     */
    function checkAutoOpenDrawer() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('show_nav') === 'true') {
                // 延迟自动打开，确保页面完全加载
                setTimeout(openNavDrawer, 100);
            }
        } catch (error) {
            console.warn('检查自动打开抽屉失败:', error);
        }
    }

    /**
     * 清理可能残留的导航状态
     */
    function cleanupNavigationState() {
        try {
            // 确保导航抽屉处于关闭状态
            if (mobileNavDrawer) {
                Utils.removeClasses(mobileNavDrawer, ['open', 'opening', 'closing', 'force-hidden']);
            }

            // 确保遮罩层处于隐藏状态
            if (mobileNavOverlay) {
                Utils.removeClasses(mobileNavOverlay, ['visible', 'force-hidden']);
            }

            // 确保body没有nav-open类
            Utils.removeClasses(document.body, 'nav-open');

            // 确保菜单按钮处于正常状态
            if (menuToggle) {
                Utils.removeClasses(menuToggle, 'active');
                const navArrow = menuToggle.querySelector('.nav-arrow polyline');
                if (navArrow) {
                    navArrow.setAttribute('points', '9 18 15 12 9 6');
                }
            }

            logNavigation('导航状态清理完成', {
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.warn('清理导航状态失败:', error);
        }
    }

    /**
     * 初始化函数
     */
    function init() {
        if (isInitialized) {
            console.warn('导航已经初始化，跳过重复初始化');
            return false;
        }

        try {
            // 记录导航初始化开始
            logNavigation('初始化开始', {
                url: window.location.href,
                userAgent: navigator.userAgent,
                screenWidth: window.innerWidth,
                screenHeight: window.innerHeight
            });

            // 首先清理可能残留的导航状态
            cleanupNavigationState();

            // 初始化二级导航
            initSubNavigation();

            // 调整导航抽屉高度以适应屏幕
            adjustNavDrawerHeight();

            // 绑定事件监听器
            bindEventListeners();

            // 检查是否需要自动展开导航抽屉
            checkAutoOpenDrawer();

            isInitialized = true;

            // 记录导航初始化完成
            logNavigation('初始化完成', {
                success: true,
                timestamp: new Date().toISOString()
            });

            return true;
        } catch (error) {
            console.error('导航初始化失败:', error);

            // 记录初始化失败
            logNavigation('初始化失败', {
                error: error.message,
                stack: error.stack
            });

            return false;
        }
    }

    /**
     * 清理资源 - 防止内存泄漏
     */
    function cleanup() {
        try {
            // 移除事件监听器
            window.removeEventListener('resize', handleResize);
            
            if (menuToggle) {
                // 克隆节点移除所有事件监听器
                const newMenuToggle = menuToggle.cloneNode(true);
                menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);
            }

            if (mobileNavClose) {
                const newNavClose = mobileNavClose.cloneNode(true);
                mobileNavClose.parentNode.replaceChild(newNavClose, mobileNavClose);
            }

            if (mobileNavOverlay) {
                const newOverlay = mobileNavOverlay.cloneNode(true);
                mobileNavOverlay.parentNode.replaceChild(newOverlay, mobileNavOverlay);
            }

            // 清理定时器
            if (resizeTimer) {
                clearTimeout(resizeTimer);
                resizeTimer = null;
            }

            // 重置状态
            isInitialized = false;
            
            console.log('导航资源清理完成');
        } catch (error) {
            console.warn('清理资源时出错:', error);
        }
    }

    /**
     * 重新初始化导航
     */
    function reinitialize() {
        cleanup();
        setTimeout(init, 100); // 延迟重新初始化
    }

    // 初始化
    if (init()) {
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', cleanup);
    }

    // 对外暴露的API
    window.MobileNavigation = {
        // 基础操作
        open: openNavDrawer,
        close: closeNavDrawer,
        toggle: function() {
            if (mobileNavDrawer && mobileNavDrawer.classList.contains('open')) {
                return closeNavDrawer();
            } else {
                return openNavDrawer();
            }
        },
        
        // 高级操作
        refresh: reinitialize,
        updateNavigation: updateActiveNavigation,
        
        // 导航项操作
        expandItem: function(moduleId) {
            const item = Array.from(mobileNavItems).find(nav => 
                nav.getAttribute('data-module-id') === moduleId
            );
            if (item) {
                return expandNavigationItem(item, moduleId, true);
            }
            return false;
        },
        
        collapseItem: function(moduleId) {
            const item = Array.from(mobileNavItems).find(nav => 
                nav.getAttribute('data-module-id') === moduleId
            );
            if (item) {
                return collapseNavigationItem(item, moduleId, true);
            }
            return false;
        },
        
        // 状态查询
        isOpen: function() {
            return mobileNavDrawer && mobileNavDrawer.classList.contains('open');
        },
        
        // 工具方法
        showMessage: showMessage,
        
        // 调试方法
        getNavigationState: StorageManager.getNavigationState,
        clearAllStates: function() {
            try {
                localStorage.removeItem('navState');
                return true;
            } catch (error) {
                console.warn('清除所有状态失败:', error);
                return false;
            }
        },

        // 重新绑定事件 - 调试用
        rebindEvents: function() {
            console.log('手动重新绑定事件');
            bindNavigationItemEvents();
        },
        
        // 获取导航日志
        getNavigationLogs: function() {
            try {
                return JSON.parse(sessionStorage.getItem('navLogs') || '[]');
            } catch (e) {
                console.warn('获取导航日志失败:', e);
                return [];
            }
        },

        // 清除导航日志
        clearNavigationLogs: function() {
            try {
                sessionStorage.removeItem('navLogs');
                return true;
            } catch (e) {
                console.warn('清除导航日志失败:', e);
                return false;
            }
        },

        // 测试返回操作修复
        testBackNavigationFix: function() {
            console.log('=== 测试返回操作修复 ===');

            // 1. 打开导航抽屉
            console.log('1. 打开导航抽屉...');
            this.open();

            setTimeout(() => {
                console.log('2. 模拟popstate事件（返回操作）...');
                // 模拟popstate事件
                const popstateEvent = new PopStateEvent('popstate', {
                    state: { isBack: true }
                });
                window.dispatchEvent(popstateEvent);

                setTimeout(() => {
                    const isOpen = this.isOpen();
                    console.log('3. 检查导航抽屉状态:', isOpen ? '仍然打开（BUG）' : '已关闭（修复成功）');

                    if (isOpen) {
                        console.log('4. 强制关闭导航抽屉...');
                        forceCloseNavDrawer();
                    }

                    console.log('=== 测试完成 ===');
                }, 500);
            }, 1000);
        },

        // 获取当前导航状态详情
        getNavigationStatus: function() {
            const status = {
                drawerOpen: mobileNavDrawer ? mobileNavDrawer.classList.contains('open') : false,
                overlayVisible: mobileNavOverlay ? mobileNavOverlay.classList.contains('visible') : false,
                bodyNavOpen: document.body.classList.contains('nav-open'),
                menuToggleActive: menuToggle ? menuToggle.classList.contains('active') : false,
                drawerClasses: mobileNavDrawer ? Array.from(mobileNavDrawer.classList) : [],
                overlayClasses: mobileNavOverlay ? Array.from(mobileNavOverlay.classList) : []
            };

            console.log('当前导航状态:', status);
            return status;
        },

        // 强制关闭（暴露给外部调用）
        forceClose: forceCloseNavDrawer
    };
});
