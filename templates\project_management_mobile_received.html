{% raw %}
<!-- "我的关注" 视图移动端专用筛选器 -->
<div class="mobile-received-filters is-mobile" style="padding: 6px 0; gap: 3px;">
    <!-- 添加标题 -->
    <div class="mobile-page-title" style="padding: 0 0 3px 0; margin-bottom: 1px;">
        <h3 style="line-height: 1.1; margin: 0;">我的关注</h3>
    </div>

    <!-- Row 1: 搜索框和搜索按钮 -->
    <div class="filter-row" style="gap: 4px; margin-bottom: 0;">
        <el-form :inline="true" @submit.prevent="statusFilters" class="mobile-search-form" style="gap: 6px;">
            <el-form-item style="margin-bottom: 0 !important;">
                <el-input
                    v-model="receivedSearchQuery"
                    placeholder="请输入关键字..."
                    clearable
                    @keyup.enter="statusFilters" />
            </el-form-item>
            <el-form-item style="margin-bottom: 0 !important;">
                <el-button type="primary" @click="statusFilters">搜索</el-button>
            </el-form-item>
        </el-form>
    </div>

    <!-- Row 2: 任务状态和截止月份 -->
    <div class="filter-row" style="gap: 4px; margin-bottom: 0;">
        <div class="filter-item-style">
            <span class="filter-label" style="margin-bottom: 1px;">任务状态：</span>
            <el-select
                v-model="receivedTaskStatus"
                placeholder="选择任务状态"
                @change="statusFilters">
                <el-option label="全部" value="all"></el-option>
                <el-option label="今日到期" value="due_today"></el-option>
                <el-option label="进行中" value="in_progress"></el-option>
                <el-option label="未完成" value="incomplete"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="已逾期未完成" value="overdue"></el-option>
                <el-option label="已逾期完成" value="overdue_completed"></el-option>
                <el-option label="按时完成" value="on_time_completed"></el-option>
            </el-select>
        </div>
        <div class="filter-item-style">
            <span class="filter-label" style="margin-bottom: 1px;">截止月份：</span>
            <el-date-picker
                v-model="receivedSelectedMonth"
                type="month"
                placeholder="按截止月份筛选"
                format="YYYY-MM"
                value-format="YYYY-MM"
                @change="handleReceivedMonthChange">
            </el-date-picker>
        </div>
    </div>

    <!-- Row 3: 任务类型按钮 -->
    <div class="filter-row" style="gap: 4px; margin-bottom: 0;">
        <div class="filter-item-style task-type-filter" style="margin: 0; padding: 0;">
            <span class="filter-label" style="margin-bottom: 1px;">任务类型：</span>
            <el-button-group >
                <el-button
                    :type="receivedTaskType === 'assigned' ? 'primary' : 'default'" 
                    @click="toggleTaskTypeFilter('assigned')"
                    :style="receivedTaskType === 'assigned' ? '' : 'border-color: rgb(240, 81, 35); color: rgb(240, 81, 35);'">
                    我负责的
                </el-button>
                <el-button style="margin-left: 10px;"
                    :type="receivedTaskType === 'created' ? 'primary' : 'default'" 
                    @click="toggleTaskTypeFilter('created')"
                    :style="receivedTaskType === 'created' ? '' : 'border-color: rgb(240, 81, 35); color: rgb(240, 81, 35);'">
                    我创建的
                </el-button>
                <el-button style="margin-left: 10px;"
                    :type="receivedTaskType === 'cc' ? 'primary' : 'default'" 
                    @click="toggleTaskTypeFilter('cc')"
                    :style="receivedTaskType === 'cc' ? '' : 'border-color: rgb(240, 81, 35); color: rgb(240, 81, 35);'">
                    被抄送的
                </el-button>
            </el-button-group>
        </div>
    </div>
</div>

<!-- 移动端专用任务表格 -->
<div class="mobile-task-table-container is-mobile">
    <!-- 加载状态显示 -->
    <div v-if="tableLoading" class="mobile-loading-container">
        <div class="mobile-loading-content">
            <div class="mobile-loading-spinner">
                <el-icon class="is-loading"><Loading /></el-icon>
            </div>
            <div class="mobile-loading-text">正在加载任务数据...</div>
            <div class="mobile-loading-skeleton">
                <!-- 骨架屏卡片 -->
                <div class="skeleton-card" v-for="n in 3" :key="n">
                    <div class="skeleton-header">
                        <div class="skeleton-checkbox"></div>
                        <div class="skeleton-title"></div>
                    </div>
                    <div class="skeleton-tags">
                        <div class="skeleton-tag"></div>
                        <div class="skeleton-tag"></div>
                        <div class="skeleton-tag"></div>
                    </div>
                    <div class="skeleton-info">
                        <div class="skeleton-info-item"></div>
                        <div class="skeleton-info-item"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务表格 -->
    <el-table
        v-if="!tableLoading"
        ref="mobileTableRef"
        :data="paginatedTasks"
        :row-class-name="getRowClass"
        @row-click="handleRowClick"
        highlight-current-row
        style="width: 100%; max-height: none !important; height: auto !important;"
        class="mobile-task-table el-table--fit el-table--enable-row-hover el-table--enable-row-transition el-table--layout-fixed"
        :empty-text="getEmptyText()">
        <el-table-column label="任务信息" min-width="80%">
            <template #default="{row}">
                <div class="mobile-task-cell">
                    <div class="mobile-task-name" :style="row.is_child ? 'padding-left: 15px' : ''">
                        <el-checkbox
                            :model-value="!!row.completed_date"
                            :disabled="!row.can_complete"
                            @click.stop
                            @change="completeTask(row)"
                            :title="getCompleteTitle(row)"
                            class="task-checkbox">
                        </el-checkbox>
                        <span :class="{
                            'task-name-text': true, 
                            'completed-name': !!row.completed_date,
                            'overdue-name': !row.completed_date && row.deadline && row.deadline < new Date().toISOString().slice(0, 10)
                        }">
                            {{ row.name }}
                        </span>
                    </div>
                    <div class="mobile-task-meta">
                        <el-tag
                            :type="row.task_relation_type === '我创建的' ? 'success' : 
                                 row.task_relation_type === '我负责的' ? 'primary' : 
                                 'warning'"
                            size="small"
                            effect="light"
                            class="mobile-task-tag">
                            {{ row.task_relation_type }}
                        </el-tag>
                        <el-tag 
                            :type="row.urgency === '紧急' ? 'danger' : 'success'" 
                            effect="plain" 
                            size="small"
                            class="mobile-task-tag">
                            {{ row.urgency }}
                        </el-tag>
                        <el-tag 
                            :type="getTaskStatusLabel(row).type" 
                            effect="light" 
                            size="small"
                            class="mobile-task-tag">
                            {{ getTaskStatusLabel(row).label }}
                        </el-tag>
                    </div>
                    <div class="mobile-task-info">
                        <span class="mobile-task-assignees" v-if="row.assignees">
                            <el-icon><User /></el-icon> {{ row.assignees }}
                        </span>
                        <span class="mobile-task-deadline" v-if="row.deadline">
                            <el-icon><Calendar /></el-icon> {{ row.deadline }}
                        </span>
                        <span class="mobile-task-completed" v-if="row.completed_date">
                            <el-icon><Check /></el-icon> {{ row.completed_date }}
                        </span>
                    </div>
                </div>
            </template>
        </el-table-column>
    </el-table>
</div>

<!-- 移动端任务表格样式 -->
<style>
.mobile-task-table-container {
    margin-top: 15px;
}

.mobile-task-cell {
    padding: 8px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.mobile-task-name {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
    gap: 8px;
    width: 100%;
    overflow: hidden;
}

/* 确保复选框和任务名称在同一行 */
.mobile-task-name .task-checkbox {
    flex-shrink: 0;
    margin-right: 0; /* 使用gap代替margin */
}

.mobile-task-name .task-name-text,
.mobile-task-name .completed-name,
.mobile-task-name .overdue-name {
    flex: 1;
    min-width: 0;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}

.mobile-task-meta {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 5px;
    flex-direction: row;
    align-items: center;
}

.mobile-task-tag {
    margin-right: 0 !important;
}

.mobile-task-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 13px;
    color: #606266;
}

.mobile-task-assignees, .mobile-task-deadline, .mobile-task-completed {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 优化移动端任务信息图标对齐 */
.mobile-task-info .el-icon {
    font-size: 14px !important;
    line-height: 1 !important;
    vertical-align: middle !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
}

/* 确保文字与图标垂直对齐 */
.mobile-task-assignees, .mobile-task-deadline, .mobile-task-completed {
    line-height: 1.2 !important;
    font-size: 13px !important;
    color: #606266 !important;
}

/* 修复图标和文字之间的间距 */
.mobile-task-info span {
    white-space: nowrap !important;
}

/* 移动端加载状态样式 */
.mobile-loading-container {
    padding: 20px;
    text-align: center;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-loading-content {
    width: 100%;
    max-width: 400px;
}

.mobile-loading-spinner {
    margin-bottom: 15px;
}

.mobile-loading-spinner .el-icon {
    font-size: 32px;
    color: #409eff;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.mobile-loading-text {
    font-size: 16px;
    color: #606266;
    margin-bottom: 25px;
    font-weight: 500;
}

/* 骨架屏样式 */
.mobile-loading-skeleton {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.skeleton-card {
    background: #fff;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.skeleton-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

.skeleton-checkbox {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-title {
    flex: 1;
    height: 18px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-tags {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.skeleton-tag {
    width: 60px;
    height: 24px;
    border-radius: 12px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-info {
    display: flex;
    gap: 15px;
}

.skeleton-info-item {
    width: 80px;
    height: 14px;
    border-radius: 3px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 移除移动端分页容器样式 */

/* 在移动设备上隐藏桌面版表格 */
@media screen and (max-width: 768px) {
    .is-desktop {
        display: none !important;
    }

    .is-mobile {
        display: block !important;
    }
}
</style> 
{% endraw %} 