<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动导航测试页面</title>
    <link rel="stylesheet" href="static/css/mobile_layout.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }
        
        .test-content {
            padding: 20px;
            margin-left: 0;
            min-height: 100vh;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #4682fa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #3366cc;
        }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        @media (min-width: 1024px) {
            .test-content {
                margin-left: 130px;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端菜单按钮 -->
    <div class="mobile-menu-toggle" id="menuToggle">
        <div class="menu-toggle">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-arrow">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        </div>
    </div>

    <!-- 移动端侧边导航抽屉 -->
    <div class="mobile-nav-drawer" id="mobileNavDrawer">
        <div class="mobile-nav-header">
            <div class="mobile-nav-user-info">
                <div class="mobile-nav-user-avatar" style="width: 40px; height: 40px; background: #4682fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">T</div>
                <span class="mobile-nav-username">测试用户</span>
            </div>
            <div class="mobile-nav-close" id="mobileNavClose">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </div>
        </div>
        
        <div class="mobile-nav-menu">
            <div class="mobile-nav-item" data-url="/test1" data-has-children="true" data-module-id="test_module">
                <div class="mobile-nav-item-content">
                    <div class="mobile-nav-icon" style="width: 24px; height: 24px; background: #4682fa; border-radius: 4px;"></div>
                    <span class="mobile-nav-title">测试模块1</span>
                    <div class="mobile-nav-arrow">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>
                </div>
                <div class="mobile-subnav-container">
                    <div class="mobile-subnav-item" data-url="/test1/sub1">子项目1</div>
                    <div class="mobile-subnav-item" data-url="/test1/sub2">子项目2</div>
                </div>
            </div>
            
            <div class="mobile-nav-item" data-url="/test2">
                <div class="mobile-nav-item-content">
                    <div class="mobile-nav-icon" style="width: 24px; height: 24px; background: #28a745; border-radius: 4px;"></div>
                    <span class="mobile-nav-title">测试模块2</span>
                </div>
            </div>
        </div>
        
        <div class="mobile-nav-footer">
            <button class="mobile-nav-logout">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                </svg>
                退出登录
            </button>
        </div>
    </div>
    
    <!-- 移动端导航遮罩层 -->
    <div class="mobile-nav-overlay" id="mobileNavOverlay"></div>

    <!-- 主要内容区域 -->
    <div class="test-content">
        <div class="test-section">
            <h2>移动导航测试页面</h2>
            <p>这个页面用于测试移动导航的左滑返回和浏览器返回功能修复。</p>
            
            <h3>测试步骤：</h3>
            <ol>
                <li>点击左侧的三角形按钮打开导航抽屉</li>
                <li>尝试以下操作：
                    <ul>
                        <li>使用浏览器的返回按钮</li>
                        <li>在支持的设备上使用左滑手势返回</li>
                        <li>点击遮罩层关闭导航</li>
                        <li>点击关闭按钮</li>
                    </ul>
                </li>
                <li>观察导航抽屉是否能正确关闭，不会卡住</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="window.MobileNavigation.open()">打开导航</button>
            <button class="test-button" onclick="window.MobileNavigation.close()">关闭导航</button>
            <button class="test-button" onclick="window.MobileNavigation.forceReset()">强制重置</button>
            <button class="test-button" onclick="window.MobileNavigation.emergencyFix()">紧急修复</button>
            <button class="test-button" onclick="showLogs()">显示日志</button>
            <button class="test-button" onclick="clearLogs()">清除日志</button>
        </div>
        
        <div class="test-section">
            <h3>导航日志</h3>
            <div id="logOutput" class="log-output">点击"显示日志"查看导航操作记录...</div>
        </div>
        
        <div class="test-section">
            <h3>模拟页面跳转</h3>
            <button class="test-button" onclick="simulateNavigation('/page1')">跳转到页面1</button>
            <button class="test-button" onclick="simulateNavigation('/page2')">跳转到页面2</button>
            <button class="test-button" onclick="history.back()">浏览器返回</button>
        </div>
    </div>

    <script src="static/js/mobile_navigation.js"></script>
    <script>
        function showLogs() {
            const logs = window.MobileNavigation.getNavigationLogs();
            const logOutput = document.getElementById('logOutput');
            if (logs.length === 0) {
                logOutput.textContent = '暂无导航日志';
            } else {
                logOutput.textContent = logs.map(log => 
                    `[${log.time}] ${log.action}: ${JSON.stringify(log.details, null, 2)}`
                ).join('\n\n');
            }
        }
        
        function clearLogs() {
            window.MobileNavigation.clearNavigationLogs();
            document.getElementById('logOutput').textContent = '日志已清除';
        }
        
        function simulateNavigation(url) {
            history.pushState({page: url}, '', url);
            document.title = `测试页面 - ${url}`;
        }
        
        // 监听popstate事件，显示返回操作
        window.addEventListener('popstate', function(event) {
            console.log('检测到popstate事件:', event);
            document.getElementById('logOutput').textContent += '\n[' + new Date().toISOString() + '] 检测到浏览器返回操作';
        });
    </script>
</body>
</html>
