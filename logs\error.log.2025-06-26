2025-06-26 00:54:47 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 00:54:47 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 00:54:47 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 00:54:48 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 00:54:48 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 00:54:48 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 00:54:48 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 00:54:48 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 00:54:48 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 00:54:48 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:07:06 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:07:06 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:07:06 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:07:06 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:07:06 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:07:06 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:07:07 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 01:07:07 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:07:07 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:07:07 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:19:15 - ERROR - CDN静态文件服务错误: js/axios.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 01:25:12 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:25:12 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:25:12 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:25:12 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:25:12 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:25:12 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:25:12 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 01:25:13 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:25:13 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:25:13 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:34:22 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:34:22 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:34:22 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:34:23 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:34:23 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:34:23 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:34:23 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 01:34:23 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 01:34:23 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 01:34:23 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 12:32:35 - ERROR - 预加载资源出错: embed.min.js, 错误: HTTPSConnectionPool(host='difys.youxiangjt.com', port=443): Read timed out. (read timeout=10)
2025-06-26 13:42:03 - ERROR - 预加载资源出错: embed.min.js, 错误: HTTPSConnectionPool(host='difys.youxiangjt.com', port=443): Read timed out. (read timeout=10)
2025-06-26 14:52:01 - ERROR - 预加载资源出错: embed.min.js, 错误: HTTPSConnectionPool(host='difys.youxiangjt.com', port=443): Read timed out. (read timeout=10)
2025-06-26 14:55:42 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 14:55:42 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 14:55:42 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 14:55:43 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 14:55:52 - ERROR - 预加载资源出错: embed.min.js, 错误: HTTPSConnectionPool(host='difys.youxiangjt.com', port=443): Read timed out. (read timeout=10)
2025-06-26 14:55:57 - ERROR - CDN静态文件服务错误: js/axios.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 15:00:49 - ERROR - 预加载资源出错: embed.min.js, 错误: HTTPSConnectionPool(host='difys.youxiangjt.com', port=443): Read timed out. (read timeout=10)
2025-06-26 17:08:30 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 17:08:30 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 17:08:30 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 17:08:31 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 17:08:31 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 17:08:31 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 17:08:31 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-26 17:08:31 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 17:08:31 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-26 17:08:31 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-26 17:15:05 - ERROR - CDN静态文件服务错误: js/axios.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
