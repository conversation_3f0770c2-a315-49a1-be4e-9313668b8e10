/* 移动端布局样式 - 深色主题版本 */

/* CSS变量定义 */
:root {
    /* 颜色变量 */
    --nav-bg-primary: #0a1424;
    --nav-text-primary: #ffffff;
    --nav-text-secondary: rgba(255, 255, 255, 0.8);
    --nav-text-tertiary: rgba(255, 255, 255, 0.6);
    --nav-hover-bg: rgba(255, 255, 255, 0.06);
    --nav-active-bg: rgba(70, 130, 250, 0.12);
    --nav-active-text: #4682fa;
    --nav-border: rgba(255, 255, 255, 0.1);
    --nav-subnav-bg: rgba(0, 0, 0, 0.1);
    --nav-overlay-bg: rgba(0, 0, 0, 0.5);
    --nav-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    --nav-glow: 0 0 12px rgba(70, 130, 250, 0.3);
    
    /* 尺寸变量 */
    --nav-width: 280px;
    --nav-padding-horizontal: 20px;
    --nav-padding-vertical: 12px;
    --nav-item-height: 48px;
    --nav-icon-size: 24px;
    --nav-font-size: 16px;
    --nav-subnav-indent: 44px;
    --nav-header-height: 64px;
    --nav-toggle-size: 40px;
    --nav-avatar-size: 40px;
    
    /* 动画变量 */
    --nav-transition: all 0.25s ease;
    --nav-drawer-transition: transform 0.3s ease;
    --nav-expand-transition: max-height 0.3s ease;
}

/* 响应式断点 */
@media (min-width: 1024px) {
    .mobile-top-bar,
    .mobile-nav-drawer,
    .mobile-nav-overlay,
    .mobile-menu-toggle {
        display: none !important;
    }
    
    .main-content {
        margin-left: 130px;
    }
}

@media (max-width: 1023px) {
    .desktop-only {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        margin-top: 0;
    }
}

/* 移除顶部导航栏 */
.mobile-top-bar {
    display: none;
}

/* 中间三角形导航控制按钮 */
.mobile-menu-toggle {
    position: fixed;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 22px;
    height: 44px;
    background-color: rgba(10, 20, 36, 0.85);
    border-radius: 0 6px 6px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    cursor: pointer;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.15);
    transition: left 0.3s cubic-bezier(0.25, 0.1, 0.25, 1), 
                transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                box-shadow 0.2s ease;
    backdrop-filter: blur(8px);
}

/* 确保悬停效果只在悬停时应用，不在active状态时应用 */
.mobile-menu-toggle:not(.active):hover {
    background-color: rgba(10, 20, 36, 0.95);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

.mobile-menu-toggle:not(.active):hover .menu-toggle svg {
    transform: scale(1.1);
}

/* 三角形箭头样式 */
.menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.menu-toggle svg {
    width: 16px;
    height: 16px;
    color: #ffffff;
    transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 导航打开时的样式 */
.mobile-menu-toggle.active {
    left: var(--nav-width);
    background-color: rgba(10, 20, 36, 0.85);
    transition: left 0.3s cubic-bezier(0.25, 0.1, 0.25, 1),
                transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                box-shadow 0.2s ease;
}

/* 移动端侧边导航抽屉 - 优化版本 */
.mobile-nav-drawer {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--nav-width);
    height: 100%;
    background-color: var(--nav-bg-primary);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.25s cubic-bezier(0.25, 0.1, 0.25, 1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    will-change: transform;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.mobile-nav-drawer.open {
    transform: translateX(0);
}

/* 确保关闭状态下抽屉完全隐藏 */
.mobile-nav-drawer:not(.open) {
    transform: translateX(-100%) !important;
}

/* 导航头部 */
.mobile-nav-header {
    height: var(--nav-header-height);
    display: flex;
    align-items: center;
    padding: 0 var(--nav-padding-horizontal);
    border-bottom: 1px solid var(--nav-border);
    position: relative;
    flex-shrink: 0;
}

/* 关闭按钮样式 */
.mobile-nav-close {
    position: absolute;
    right: var(--nav-padding-horizontal);
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: var(--nav-text-primary);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: var(--nav-transition);
}

.mobile-nav-close:hover {
    background-color: var(--nav-hover-bg);
    color: var(--nav-text-primary);
}

/* 用户信息区域 */
.mobile-nav-user-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.mobile-nav-user-avatar {
    width: var(--nav-avatar-size);
    height: var(--nav-avatar-size);
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid var(--nav-border);
}

.mobile-nav-username {
    color: var(--nav-text-primary);
    font-size: var(--nav-font-size);
    font-weight: 500;
    margin-left: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 移动端导航菜单 */
.mobile-nav-menu {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.mobile-nav-menu::-webkit-scrollbar {
    width: 4px;
}

.mobile-nav-menu::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

.mobile-nav-menu::-webkit-scrollbar-thumb {
    background: var(--nav-text-tertiary);
    border-radius: 2px;
}

/* 一级导航项 */
.mobile-nav-item {
    position: relative;
    margin: 2px 8px;
    border-radius: 10px;
    overflow: hidden;
}

.mobile-nav-item-content {
    display: flex;
    align-items: center;
    padding: var(--nav-padding-vertical) var(--nav-padding-horizontal);
    width: 100%;
    font-size: var(--nav-font-size);
    color: var(--nav-text-secondary);
    transition: var(--nav-transition);
    min-height: var(--nav-item-height);
    cursor: pointer;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

/* 统一的悬停效果 */
.mobile-nav-item:hover .mobile-nav-item-content {
    color: var(--nav-text-primary);
}

/* 一级菜单激活状态 - 新的选中样式 */
.mobile-nav-item.active .mobile-nav-item-content {
    background: linear-gradient(135deg, var(--nav-active-bg), rgba(70, 130, 250, 0.08));
    color: var(--nav-text-primary);
    box-shadow: var(--nav-glow);
    position: relative;
}

/* 添加微妙的发光效果 */
.mobile-nav-item.active .mobile-nav-item-content::before {
    content: none;
}

.mobile-nav-item.active .mobile-nav-title {
    color: var(--nav-active-text);
    font-weight: 600;
}

.mobile-nav-item.active .mobile-nav-icon {
    color: var(--nav-active-text);
    filter: none;
}

.mobile-nav-icon {
    width: var(--nav-icon-size);
    height: var(--nav-icon-size);
    margin-right: 12px;
    flex-shrink: 0;
    transition: var(--nav-transition);
}

.mobile-nav-title {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: var(--nav-transition);
}

/* 箭头样式 */
.mobile-nav-arrow {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nav-text-tertiary);
    transition: var(--nav-transition);
    margin-left: 8px;
    flex-shrink: 0;
    border-radius: 4px;
}

.mobile-nav-item:hover .mobile-nav-arrow {
    color: var(--nav-text-primary);
    background-color: rgba(255, 255, 255, 0.1);
}

.mobile-nav-item.active .mobile-nav-arrow {
    color: var(--nav-active-text);
}

/* 展开状态的箭头 */
.mobile-nav-item.expanded .mobile-nav-arrow {
    transform: rotate(90deg);
}

/* 二级导航样式 */
.mobile-subnav-container {
    max-height: 0;
    overflow: hidden;
    overflow-y: clip;
    transition: var(--nav-expand-transition);
    background-color: var(--nav-subnav-bg);
    margin: 0 8px;
    border-radius: 0 0 10px 10px;
}

.mobile-nav-item.expanded .mobile-subnav-container {
    max-height: 400px;
    overflow-y: auto;
}

.mobile-subnav-container::-webkit-scrollbar {
    width: 3px;
}

.mobile-subnav-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

.mobile-subnav-container::-webkit-scrollbar-thumb {
    background: var(--nav-text-tertiary);
    border-radius: 2px;
}

.mobile-subnav-item {
    display: flex;
    align-items: center;
    padding: 10px var(--nav-padding-horizontal);
    padding-left: var(--nav-subnav-indent);
    color: var(--nav-text-secondary);
    font-size: 15px;
    transition: var(--nav-transition);
    min-height: 44px;
    cursor: pointer;
    margin: 4px 0;
    border-radius: 8px;
    position: relative;
}

/* 二级菜单悬停效果 */
.mobile-subnav-item:hover {
    color: var(--nav-text-primary);
}

/* 二级菜单激活状态 - 优化选中样式，减小阴影范围 */
.mobile-subnav-item.active {
    background: linear-gradient(135deg, var(--nav-active-bg), rgba(70, 130, 250, 0.08));
    color: var(--nav-active-text);
    font-weight: 600;
    border-radius: 8px;
    box-shadow: 0 0 4px rgba(70, 130, 250, 0.2);
    position: relative;
}

/* 二级菜单激活状态的微妙发光效果 - 缩小影响范围 */
.mobile-subnav-item.active::before {
    content: none;
}

/* 当二级菜单激活时，一级菜单保持普通状态 */
.mobile-nav-item:has(.mobile-subnav-item.active) .mobile-nav-item-content {
    background: transparent;
    box-shadow: none;
}

.mobile-nav-item:has(.mobile-subnav-item.active) .mobile-nav-item-content::before {
    display: none;
}

.mobile-nav-item:has(.mobile-subnav-item.active) .mobile-nav-title {
    color: var(--nav-text-secondary);
    font-weight: normal;
}

.mobile-nav-item:has(.mobile-subnav-item.active) .mobile-nav-icon {
    color: var(--nav-text-secondary);
    filter: none;
}

/* 移动端导航底部 */
.mobile-nav-footer {
    padding: var(--nav-padding-horizontal);
    border-top: 1px solid var(--nav-border);
    flex-shrink: 0;
}

.mobile-nav-logout {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: var(--nav-text-secondary);
    font-size: var(--nav-font-size);
    padding: var(--nav-padding-vertical) 0;
    cursor: pointer;
    width: 100%;
    transition: var(--nav-transition);
    min-height: var(--nav-item-height);
    border-radius: 6px;
}

.mobile-nav-logout:hover {
    color: var(--nav-text-primary);
    background-color: var(--nav-hover-bg);
    padding-left: 8px;
}

.mobile-nav-logout svg {
    margin-right: 12px;
    width: var(--nav-icon-size);
    height: var(--nav-icon-size);
}

/* 遮罩层 - 优化版本 */
.mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--nav-overlay-bg);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    will-change: opacity, visibility;
}

.mobile-nav-overlay.visible {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* 确保遮罩层在隐藏时完全不可交互 */
.mobile-nav-overlay:not(.visible) {
    pointer-events: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* 禁止滚动 */
body.nav-open {
    overflow: hidden;
}

/* 消息提示组件 */
.mobile-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(10, 20, 36, 0.95);
    color: var(--nav-text-primary);
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: var(--nav-shadow);
    z-index: 2000;
    max-width: 90%;
    font-size: 15px;
    text-align: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(8px);
}

.mobile-message.visible {
    opacity: 1;
    visibility: visible;
}

/* 焦点状态 - 增强无障碍访问 */
.mobile-nav-item-content:focus,
.mobile-subnav-item:focus,
.mobile-nav-logout:focus {
    outline: 2px solid var(--nav-active-text);
    outline-offset: -2px;
}

/* 过渡动画优化 */
.mobile-nav-item-content,
.mobile-subnav-item,
.mobile-nav-arrow,
.mobile-nav-title,
.mobile-nav-icon {
    will-change: transform, background-color, color;
}

/* 移除不必要的样式类 */
.mobile-nav-user,
.mobile-nav-logo {
    display: none;
}

/* 确保图标为白色且更明显 */
.menu-icon {
    filter: brightness(0) invert(1);
}
